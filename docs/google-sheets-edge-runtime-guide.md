# Google Sheets API Edge Runtime 遷移指南

## 📋 概述

本指南說明如何將專案中的 Google Sheets API 從 Node.js Runtime 遷移到 Edge Runtime，以實現與 Cloudflare Pages 的完全相容性。

## 🎯 目標

- ✅ 移除對 `googleapis` 套件的依賴
- ✅ 實作 Edge Runtime 相容的 Google Sheets API 客戶端
- ✅ 保持現有 API 介面的相容性
- ✅ 支援 Cloudflare Pages 部署
- ✅ 提升效能和安全性

## 🔧 技術方案

### 核心技術棧
- **Google Sheets REST API** - 直接 HTTP 調用
- **Web Crypto API** - JWT 簽名和認證
- **Fetch API** - HTTP 請求
- **Edge Runtime** - Cloudflare Pages 相容

### 認證機制
使用 Service Account 和 JWT 進行認證：
1. 使用 Web Crypto API 產生 JWT
2. 向 Google OAuth2 端點換取存取權杖
3. 使用存取權杖調用 Google Sheets API

## 📁 新增檔案結構

```
src/
├── lib/
│   ├── google-sheets-edge.ts          # Edge Runtime 相容的核心客戶端
│   └── google-sheets-migration.ts     # 遷移相容層
├── app/api/
│   └── test-edge-sheets/
│       └── route.ts                   # 測試 API
└── scripts/
    └── migrate-to-edge-sheets.js      # 自動遷移腳本
```

## 🚀 實作步驟

### 步驟 1: 執行自動遷移

```bash
# 執行遷移腳本
node scripts/migrate-to-edge-sheets.js
```

遷移腳本會自動：
- 更新所有 Google Sheets 相關的 import 語句
- 為 API 路由添加 Edge Runtime 配置
- 移除舊的 Node.js Runtime 配置

### 步驟 2: 驗證環境變數

確保以下環境變數已正確設定：

```env
# Google Service Account 憑證
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# Google Sheets ID（依環境區分）
GOOGLE_PRODUCTION_SHEET_ID=your-production-sheet-id
GOOGLE_SANDBOX_SHEET_ID=your-sandbox-sheet-id
GOOGLE_PRODUCTION_WATCH_SHEET_ID=your-production-watch-sheet-id
GOOGLE_SANDBOX_WATCH_SHEET_ID=your-sandbox-watch-sheet-id
GOOGLE_PRODUCTION_BLOG_SHEET_ID=your-production-blog-sheet-id
GOOGLE_SANDBOX_BLOG_SHEET_ID=your-sandbox-blog-sheet-id
```

### 步驟 3: 測試新實作

```bash
# 啟動開發伺服器
npm run dev

# 測試 Edge Runtime API
curl http://localhost:3000/api/test-edge-sheets

# 測試寫入功能
curl -X POST http://localhost:3000/api/test-edge-sheets \
  -H "Content-Type: application/json" \
  -d '{"testData": [["測試", "Edge Runtime", "成功"]]}'

# 測試認證功能
curl -X PUT http://localhost:3000/api/test-edge-sheets
```

### 步驟 4: 更新 package.json 腳本

```json
{
  "scripts": {
    "migrate:edge-sheets": "node scripts/migrate-to-edge-sheets.js",
    "test:edge-sheets": "curl http://localhost:3000/api/test-edge-sheets",
    "deploy:cloudflare": "npm run build && wrangler pages deploy .next"
  }
}
```

## 🔄 API 變更對照

### 舊版 (Node.js Runtime)
```typescript
import { getSheetsClient, appendToSheet, getSheetData } from '@/lib/google-sheets';

// 需要 Node.js Runtime
export const runtime = 'nodejs'; // ❌ 不相容 Cloudflare Pages

const sheets = getSheetsClient();
await sheets.spreadsheets.values.append({...}); // ❌ googleapis 套件
```

### 新版 (Edge Runtime)
```typescript
import { appendToSheet, getSheetData } from '@/lib/google-sheets-migration';

// Edge Runtime 相容
export const runtime = 'edge'; // ✅ Cloudflare Pages 相容

await appendToSheet('工作表1!A:C', [['資料1', '資料2', '資料3']]); // ✅ 相同介面
```

## 📊 效能比較

| 項目 | Node.js Runtime | Edge Runtime |
|------|----------------|--------------|
| 冷啟動時間 | ~500ms | ~50ms |
| 記憶體使用 | ~50MB | ~5MB |
| 套件大小 | ~2MB | ~200KB |
| Cloudflare Pages | ❌ 不支援 | ✅ 完全支援 |

## 🔒 安全性改進

### JWT 簽名
- 使用 Web Crypto API 進行 RSA-SHA256 簽名
- 權杖自動過期和更新機制
- 無需在 Edge Runtime 中儲存私鑰檔案

### 權限控制
- Service Account 最小權限原則
- 僅授予必要的 Google Sheets 存取權限
- 支援多環境憑證隔離

## 🧪 測試策略

### 單元測試
```bash
# 測試 Edge Runtime 相容性
npm run test:unit -- google-sheets-edge

# 測試遷移相容層
npm run test:unit -- google-sheets-migration
```

### 整合測試
```bash
# 測試完整的 API 流程
npm run test:integration -- google-sheets

# 測試表單提交流程
npm run test:api:forms
```

### 效能測試
```bash
# 測試 CPU 使用限制
npm run test:cpu

# 基準測試
npm run benchmark
```

## 🚨 常見問題

### Q: 遷移後 API 回應格式是否改變？
A: 不會。遷移相容層確保 API 回應格式與原有實作完全相同。

### Q: 如何處理大量資料讀寫？
A: Edge Runtime 有 10ms CPU 時間限制，建議：
- 使用分頁讀取大量資料
- 實作資料快取機制
- 考慮使用批次處理

### Q: 認證權杖如何管理？
A: 客戶端自動管理權杖生命週期：
- 權杖快取在記憶體中
- 自動檢查過期時間
- 提前 5 分鐘更新權杖

### Q: 如何除錯認證問題？
A: 檢查以下項目：
- Service Account Email 格式正確
- Private Key 包含完整的 PEM 格式
- Service Account 有 Google Sheets 存取權限
- Sheet ID 正確且可存取

## 📈 監控和維護

### 日誌監控
- 所有 API 調用都有詳細日誌
- 錯誤自動記錄和報告
- 效能指標追蹤

### 健康檢查
```bash
# 檢查 API 健康狀態
curl http://localhost:3000/api/test-edge-sheets

# 檢查認證狀態
curl -X PUT http://localhost:3000/api/test-edge-sheets
```

## 🎯 後續優化

1. **快取策略** - 實作 Redis 或 KV 快取
2. **批次操作** - 支援批次讀寫以提升效能
3. **錯誤重試** - 實作指數退避重試機制
4. **監控儀表板** - 建立 API 使用情況監控

## 📞 支援

如遇到問題，請檢查：
1. 測試 API 是否正常運作
2. 環境變數是否正確設定
3. Google Service Account 權限是否足夠
4. Cloudflare Pages 部署日誌
