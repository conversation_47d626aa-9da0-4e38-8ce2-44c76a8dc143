# MDX 檔案系統 Edge Runtime 遷移技術分析報告

## 📋 執行摘要

本報告分析專案中使用 Node.js `fs` 和 `path` 模組的 MDX 檔案處理功能，評估其在 Edge Runtime 環境下的相容性，並提供最佳的遷移解決方案。

## 🔍 現況分析

### 受影響的檔案和功能

#### 1. 核心檔案：`src/lib/mdx-utils.ts`
**Node.js 依賴：**
- `fs.existsSync()` - 檢查目錄/檔案存在
- `fs.readdirSync()` - 讀取目錄內容
- `fs.readFileSync()` - 讀取檔案內容
- `path.join()` - 路徑組合
- `process.cwd()` - 取得當前工作目錄

**核心功能：**
- 掃描 `content/blog/` 目錄中的 MDX 檔案
- 解析 frontmatter 和內容
- 提供分頁、搜尋、標籤過濾功能
- 計算閱讀時間

#### 2. 相關 API 路由
- `/api/blog` - 部落格文章列表（分頁）
- `/api/blog/[slug]` - 單一文章詳情
- `/api/cache-performance` - 快取效能測試（部分功能）

#### 3. 資料結構
```
content/blog/
├── 2024-10-22-is-watch-winder-necessary.mdx
├── 2024-11-21-omega-speedmaster-from-hesalite-to-sapphire.mdx
├── 2025-02-16-how-to-clean-bracelets-or-straps-of-watches.mdx
├── 2025-03-07-5-things-about-watch-water-resistance.mdx
└── 2025-04-29-in-house-movement-and-third-party-movement-guide.mdx
```

## ⚠️ Edge Runtime 相容性問題

### 不相容的 Node.js API
1. **檔案系統存取**：Edge Runtime 無法存取本地檔案系統
2. **同步 I/O 操作**：`fs.readFileSync()` 等同步操作不被支援
3. **Process 物件**：`process.cwd()` 在 Edge Runtime 中不可用

### 技術限制
- Edge Runtime 基於 Web Standards，不提供檔案系統 API
- 無法在運行時動態讀取本地檔案
- 靜態檔案必須在建置時處理

## 🎯 解決方案評估

### 方案 1：建置時預處理（推薦）⭐⭐⭐⭐⭐

**概念：**
在建置時將所有 MDX 檔案處理成 JSON 資料，Edge Runtime API 直接讀取預處理的資料。

**優點：**
- ✅ 完全相容 Edge Runtime
- ✅ 最佳效能（無檔案 I/O）
- ✅ 支援 Cloudflare Pages 靜態部署
- ✅ 快取友善
- ✅ 可預先驗證內容格式

**缺點：**
- ❌ 需要重新建置才能更新內容
- ❌ 增加建置時間

**實作複雜度：** 中等

### 方案 2：混合架構（部分推薦）⭐⭐⭐⭐

**概念：**
保留部落格 API 為 Node.js Runtime，其他 API 使用 Edge Runtime。

**優點：**
- ✅ 最小程式碼變更
- ✅ 保持動態檔案讀取能力
- ✅ 其他 API 仍享受 Edge Runtime 優勢

**缺點：**
- ❌ 部分 API 無法享受 Edge Runtime 效能
- ❌ 架構複雜性增加
- ❌ Cloudflare Pages 可能有限制

**實作複雜度：** 低

### 方案 3：外部 CMS 遷移⭐⭐⭐

**概念：**
將部落格內容遷移到 Headless CMS（如 Strapi、Contentful）。

**優點：**
- ✅ 完全相容 Edge Runtime
- ✅ 內容管理介面
- ✅ 即時更新
- ✅ 多人協作

**缺點：**
- ❌ 需要額外服務和成本
- ❌ 大量遷移工作
- ❌ 增加系統複雜度

**實作複雜度：** 高

## 🚀 推薦解決方案：建置時預處理

### 技術架構

```mermaid
graph TD
    A[MDX 檔案] --> B[建置時處理腳本]
    B --> C[生成 JSON 資料]
    C --> D[Edge Runtime API]
    D --> E[前端應用]
    
    F[新增/修改 MDX] --> G[重新建置]
    G --> H[更新 JSON 資料]
```

### 實作步驟

#### 1. 建立建置時處理腳本
```typescript
// scripts/process-mdx.ts
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

interface ProcessedPost {
  slug: string;
  frontmatter: BlogFrontmatter;
  content: string;
  readingTime: number;
}

export function processMdxFiles(): {
  posts: ProcessedPost[];
  metadata: {
    totalPosts: number;
    lastUpdated: string;
    tags: string[];
  };
} {
  // 處理邏輯...
}
```

#### 2. 建立 Edge Runtime 相容的資料存取層
```typescript
// src/lib/blog-data.ts
import blogData from '@/data/blog.json';

export function getAllPosts(): ProcessedPost[] {
  return blogData.posts;
}

export function getPostBySlug(slug: string): ProcessedPost | null {
  return blogData.posts.find(post => post.slug === slug) || null;
}

export function getPaginatedPosts(page: number, pageSize: number) {
  // 分頁邏輯...
}
```

#### 3. 更新 API 路由
```typescript
// src/app/api/blog/route.ts
import { getPaginatedPosts } from '@/lib/blog-data';

export const runtime = 'edge'; // 啟用 Edge Runtime

export async function GET(request: Request) {
  // 使用預處理的資料...
}
```

### 效能預期

| 指標 | 現況 (Node.js) | 預期 (Edge Runtime) | 改善 |
|------|----------------|-------------------|------|
| 冷啟動時間 | ~500ms | ~50ms | 90% ⬇️ |
| API 回應時間 | ~200ms | ~20ms | 90% ⬇️ |
| 記憶體使用 | ~50MB | ~5MB | 90% ⬇️ |
| 併發處理 | 有限 | 高併發 | 10x ⬆️ |

## 📋 實作計劃

### Phase 1: 建置腳本開發（1-2 天）
- [ ] 建立 MDX 處理腳本
- [ ] 設定 JSON 輸出格式
- [ ] 整合到建置流程

### Phase 2: 資料存取層重構（1 天）
- [ ] 建立 Edge Runtime 相容的資料存取函數
- [ ] 保持原有 API 介面相容性
- [ ] 添加錯誤處理

### Phase 3: API 路由更新（1 天）
- [ ] 更新部落格 API 路由
- [ ] 添加 Edge Runtime 配置
- [ ] 測試功能完整性

### Phase 4: 測試和優化（1 天）
- [ ] 功能測試
- [ ] 效能測試
- [ ] 部署驗證

## 🔧 技術實作細節

### 建置腳本整合
```json
// package.json
{
  "scripts": {
    "build": "npm run process-mdx && next build",
    "process-mdx": "tsx scripts/process-mdx.ts",
    "dev": "npm run process-mdx && next dev"
  }
}
```

### 資料格式設計
```typescript
interface BlogData {
  posts: ProcessedPost[];
  metadata: {
    totalPosts: number;
    lastUpdated: string;
    tags: string[];
    categories: string[];
  };
  index: {
    bySlug: Record<string, number>;
    byTag: Record<string, number[]>;
    byDate: Record<string, number[]>;
  };
}
```

## 📊 風險評估

### 高風險
- **內容更新流程變更**：需要重新建置才能更新內容

### 中風險
- **建置時間增加**：處理 MDX 檔案需要額外時間
- **JSON 檔案大小**：隨著文章增加可能影響載入速度

### 低風險
- **功能相容性**：現有 API 介面保持不變
- **SEO 影響**：靜態生成對 SEO 更有利

## 🎯 結論與建議

**推薦採用建置時預處理方案**，理由如下：

1. **完全 Edge Runtime 相容**：解決所有 Node.js 依賴問題
2. **最佳效能表現**：預處理資料提供最快的存取速度
3. **Cloudflare Pages 友善**：完全支援靜態部署
4. **可擴展性**：未來可輕鬆添加搜尋索引、標籤索引等功能
5. **維護成本低**：一次性實作，長期穩定運行

**實作優先級：** 高
**預估工作量：** 4-5 天
**技術風險：** 低
**業務影響：** 最小（保持 API 相容性）

這個方案將使部落格功能完全相容 Edge Runtime，同時提供最佳的效能表現。
