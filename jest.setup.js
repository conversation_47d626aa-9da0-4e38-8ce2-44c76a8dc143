require('@testing-library/jest-dom')

// Polyfill for TextEncoder/TextDecoder (needed for MSW)
const { TextEncoder, TextDecoder } = require('util')
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Polyfill for Web Crypto API (needed for Edge Runtime)
const crypto = require('crypto')

// Mock crypto.subtle for Edge Runtime compatibility
global.crypto = {
  ...global.crypto,
  subtle: {
    importKey: jest.fn().mockResolvedValue({
      type: 'secret',
      extractable: false,
      algorithm: { name: 'AES-GCM' },
      usages: ['encrypt', 'decrypt']
    }),
    encrypt: jest.fn().mockImplementation(async (algorithm, key, data) => {
      // 模擬 AES-GCM 加密，返回包含 auth tag 的結果
      const mockCipherText = new Uint8Array(data.byteLength);
      const mockAuthTag = new Uint8Array(16); // GCM auth tag 是 16 字節

      // 填充一些模擬數據
      for (let i = 0; i < mockCipherText.length; i++) {
        mockCipherText[i] = i % 256;
      }
      for (let i = 0; i < mockAuthTag.length; i++) {
        mockAuthTag[i] = (i + 100) % 256;
      }

      // 合併密文和 auth tag
      const result = new Uint8Array(mockCipherText.length + mockAuthTag.length);
      result.set(mockCipherText, 0);
      result.set(mockAuthTag, mockCipherText.length);

      return result.buffer;
    }),
    decrypt: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
    sign: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
    verify: jest.fn().mockResolvedValue(true),
    digest: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
    generateKey: jest.fn().mockResolvedValue({}),
    deriveBits: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
    deriveKey: jest.fn().mockResolvedValue({}),
    wrapKey: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
    unwrapKey: jest.fn().mockResolvedValue({})
  },
  getRandomValues: (arr) => {
    return crypto.randomFillSync(arr)
  }
}

// Polyfill for Web APIs needed by Next.js API routes
if (!global.Request) {
  // Simple polyfills for testing
  global.Request = class Request {
    constructor(url, options = {}) {
      // 使用 Object.defineProperty 來設定 url 屬性
      Object.defineProperty(this, 'url', {
        value: url,
        writable: false,
        enumerable: true,
        configurable: false
      })
      this.method = options.method || 'GET'
      this.headers = new Map(Object.entries(options.headers || {}))
      this.body = options.body
    }

    async json() {
      return JSON.parse(this.body)
    }

    async formData() {
      return this.body
    }
  }

  global.Response = class Response {
    constructor(body, options = {}) {
      this.body = body
      this.status = options.status || 200
      this.headers = new Map(Object.entries(options.headers || {}))
    }

    async json() {
      return JSON.parse(this.body)
    }

    static json(data, options = {}) {
      return new Response(JSON.stringify(data), {
        status: options.status || 200,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      })
    }
  }

  global.Headers = Map

  // URL polyfill
  if (!global.URL) {
    global.URL = class URL {
      constructor(url, base) {
        if (base) {
          this.href = new URL(url, base).href
        } else {
          this.href = url
        }

        // 簡單的 URL 解析
        const match = this.href.match(/^(https?:)\/\/([^\/]+)(\/.*)?(\?.*)?$/)
        if (match) {
          this.protocol = match[1]
          this.host = match[2]
          this.pathname = match[3] || '/'
          this.search = match[4] || ''
        }
      }

      toString() {
        return this.href
      }
    }
  }

  global.FormData = class FormData {
    constructor() {
      this.data = new Map()
    }

    append(key, value) {
      this.data.set(key, value)
    }

    get(key) {
      return this.data.get(key)
    }

    entries() {
      return this.data.entries()
    }
  }
}

// 暫時禁用 MSW，專注於基本測試功能
// import { server } from './src/__tests__/mocks/server'

// 在所有測試開始前啟動 MSW 服務器
// beforeAll(() => {
//   server.listen({
//     onUnhandledRequest: 'warn', // 對未處理的請求發出警告
//   });
// });

// 在每個測試後重置處理器，確保測試間不互相影響
// afterEach(() => {
//   server.resetHandlers();
// });

// 在所有測試結束後關閉 MSW 服務器
// afterAll(() => {
//   server.close();
// });

// Mock NextResponse
global.NextResponse = {
  json: (data, options = {}) => {
    return new global.Response(JSON.stringify(data), {
      status: options.status || 200,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    })
  },
  redirect: (url, status = 302) => {
    return new global.Response(null, {
      status,
      headers: {
        'Location': url
      }
    })
  }
}

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return ''
  },
}))

// Mock useSessionAvailability hook to prevent act() warnings
jest.mock('@/hooks/useSessionAvailability', () => ({
  useSessionAvailability: jest.fn(() => ({
    availability: [
      {
        sessionTime: '台北 07/20（日）13:20',
        maxCapacity: 10,
        registeredCount: 5,
        availableSpots: 5,
        isAvailable: true,
        showAvailability: false
      },
      {
        sessionTime: '台北 07/20（日）15:20',
        maxCapacity: 10,
        registeredCount: 8,
        availableSpots: 2,
        isAvailable: true,
        showAvailability: true
      },
      {
        sessionTime: '台中 07/18（五）19:20',
        maxCapacity: 8,
        registeredCount: 3,
        availableSpots: 5,
        isAvailable: true,
        showAvailability: false
      }
    ],
    loading: false,
    error: null,
    refetch: jest.fn()
  })),
  isSessionAvailable: (availability, sessionName) => {
    const session = availability.find(s => s.sessionTime === sessionName);
    return session ? session.isAvailable : true;
  },
  getAvailabilityText: (availability, sessionName) => {
    const session = availability.find(s => s.sessionTime === sessionName);
    if (!session) return '';
    if (!session.isAvailable) return '已額滿';
    if (session.showAvailability) return `剩餘 ${session.availableSpots} 名額`;
    return '';
  }
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock Google Sheets API
jest.mock('@/lib/google-sheets', () => ({
  getSheetsClient: jest.fn(() => ({
    spreadsheets: {
      values: {
        get: jest.fn().mockResolvedValue({
          data: {
            values: [
              ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC'],
              ['張三', '台北 07/20（日）13:20', '1', '1', '<EMAIL>', '0912345678', '男', '1990-01-01', '台北市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'],
              ['李四', '台北 07/20（日）13:20', '1', '1', '<EMAIL>', '0912345679', '女', '1985-05-15', '新北市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'],
              ['王五', '台北 07/20（日）13:20', '2', '雙人團報', '<EMAIL>', '0912345680', '男', '1992-03-10', '桃園市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'],
              ['趙六', '台北 07/20（日）13:20', '1', '3', '<EMAIL>', '0912345681', '女', '1988-12-25', '台中市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '3']
            ]
          }
        }),
        append: jest.fn().mockResolvedValue({
          data: {
            updates: {
              updatedRows: 1
            }
          }
        }),
        update: jest.fn().mockResolvedValue({
          data: {
            updatedRows: 1
          }
        })
      }
    }
  })),
  appendToSheet: jest.fn().mockResolvedValue({
    success: true,
    rowIndex: 5
  }),
  readFromSheet: jest.fn().mockResolvedValue([
    ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC'],
    ['張三', '台北 07/20（日）13:20', '1', '1', '<EMAIL>', '0912345678', '男', '1990-01-01', '台北市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'],
    ['李四', '台北 07/20（日）13:20', '1', '1', '<EMAIL>', '0912345679', '女', '1985-05-15', '新北市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1']
  ])
}))

// Mock PayUni Edge functions
jest.mock('@/lib/payuni-edge', () => ({
  createPaymentRequestEdge: jest.fn().mockImplementation((params) => {
    return Promise.resolve({
      MerID: 'S01421169',
      EncryptInfo: 'encrypted_data_here',
      HashInfo: 'hash_info_here',
      ApiUrl: 'https://sandbox-api.payuni.com.tw/api/payment',
      Timestamp: Math.floor(Date.now() / 1000)
    });
  }),
  queryPayUniOrder: jest.fn().mockResolvedValue({
    Status: '1',
    Message: '付款成功',
    Result: {
      MerID: 'S01421169',
      MerTradeNo: 'pangea_1234567890',
      TradeNo: 'TP12345678901234567890',
      TradeAmt: 1500,
      PaymentType: 'CREDIT',
      CreateTime: '2025-08-07 21:23:06',
      PayTime: '2025-08-07 21:25:30'
    }
  }),
  calculateATMExpireDate: jest.fn().mockImplementation(() => {
    // 模擬正確的日期計算邏輯
    const now = new Date();
    const taiwanTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
    const hour = taiwanTime.getUTCHours(); // 使用 UTC 小時，因為 taiwanTime 已經是 UTC+8

    let daysToAdd = 2;
    if (hour >= 14) { // 使用 >= 而不是 >
      daysToAdd = 3;
    }

    const expireDate = new Date(taiwanTime);
    expireDate.setDate(expireDate.getDate() + daysToAdd);
    return expireDate.toISOString().split('T')[0];
  }),
  calculateATMExpireDateEdge: jest.fn().mockImplementation(() => {
    // 模擬正確的日期計算邏輯
    const now = new Date();
    const taiwanTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
    const hour = taiwanTime.getUTCHours(); // 使用 UTC 小時，因為 taiwanTime 已經是 UTC+8

    let daysToAdd = 2;
    if (hour >= 14) { // 使用 >= 而不是 >
      daysToAdd = 3;
    }

    const expireDate = new Date(taiwanTime);
    expireDate.setDate(expireDate.getDate() + daysToAdd);
    return expireDate.toISOString().split('T')[0];
  }),
  getSheetData: jest.fn().mockResolvedValue([
    ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC'],
    ['張三', '台北 07/20（日）13:20', '1', '1', '<EMAIL>', '0912345678', '男', '1990-01-01', '台北市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'],
    ['李四', '台北 07/20（日）13:20', '1', '1', '<EMAIL>', '0912345679', '女', '1985-05-15', '新北市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'],
    ['王五', '台北 07/20（日）13:20', '2', '雙人團報', '<EMAIL>', '0912345680', '男', '1992-03-10', '桃園市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'],
    ['趙六', '台北 07/20（日）13:20', '1', '3', '<EMAIL>', '0912345681', '女', '1988-12-25', '台中市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '3']
  ])
}))

// Mock Google Sheets Migration functions
jest.mock('@/lib/google-sheets-migration', () => ({
  getSheetData: jest.fn().mockResolvedValue([
    ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC'],
    ['張三', '台北 07/20（日）13:20', '1', '1', '<EMAIL>', '0912345678', '男', '1990-01-01', '台北市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'],
    ['李四', '台北 07/20（日）13:20', '1', '1', '<EMAIL>', '0912345679', '女', '1985-05-15', '新北市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'],
    ['王五', '台北 07/20（日）13:20', '2', '雙人團報', '<EMAIL>', '0912345680', '男', '1992-03-10', '桃園市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'],
    ['趙六', '台北 07/20（日）13:20', '1', '3', '<EMAIL>', '0912345681', '女', '1988-12-25', '台中市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '3']
  ]),
  appendToSheetEdge: jest.fn().mockResolvedValue({
    success: true,
    rowIndex: 5
  }),
  getSheetsClientEdge: jest.fn().mockReturnValue({
    spreadsheets: {
      values: {
        get: jest.fn().mockResolvedValue({
          data: {
            values: [
              ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC'],
              ['張三', '台北 07/20（日）13:20', '1', '1', '<EMAIL>', '0912345678', '男', '1990-01-01', '台北市', '無', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1']
            ]
          }
        }),
        append: jest.fn().mockResolvedValue({
          data: {
            updates: {
              updatedRows: 1
            }
          }
        }),
        update: jest.fn().mockResolvedValue({
          data: {
            updatedRows: 1
          }
        })
      }
    }
  })
}))

// Mock PAYUNI_CONFIG and RECAPTCHA_CONFIG
jest.mock('@/config/environment-config', () => ({
  PAYUNI_CONFIG: {
    getMerchantId: jest.fn().mockReturnValue('S01421169'),
    getHashKey: jest.fn().mockReturnValue('test_hash_key'),
    getHashIV: jest.fn().mockReturnValue('test_hash_iv'),
    getApiUrl: jest.fn().mockReturnValue('https://sandbox-api.payuni.com.tw/api/payment'),
    isProduction: jest.fn().mockReturnValue(false)
  },
  RECAPTCHA_CONFIG: {
    getSiteKey: jest.fn().mockImplementation(() => {
      return process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || '';
    }),
    getSecretKey: jest.fn().mockImplementation(() => {
      return process.env.RECAPTCHA_SECRET_KEY || '';
    }),
    getVerifyUrl: jest.fn().mockReturnValue('https://www.google.com/recaptcha/api/siteverify')
  }
}))
