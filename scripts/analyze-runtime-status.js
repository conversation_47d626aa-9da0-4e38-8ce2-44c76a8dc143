#!/usr/bin/env node

/**
 * Edge Runtime 狀態分析工具
 * 掃描所有 API routes 檔案，分析它們的 runtime 配置狀態
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class RuntimeAnalyzer {
  constructor() {
    this.apiDir = path.join(__dirname, '../src/app/api');
    this.results = {
      total: 0,
      edgeRuntime: [],
      nodeRuntime: [],
      noRuntimeSpecified: [],
      nodeJsOnlyRoutes: [],
      errors: []
    };

    // 從 add-edge-runtime.js 複製的黑名單
    this.nodeJsOnlyRoutes = [
      'contact',                    // 使用 Google Sheets
      'event-registration',         // 使用 Google Sheets
      'register',                   // 使用 Google Sheets
      'pangea-appointment',         // 使用 Google Sheets
      'watch-appointment',          // 使用 Google Sheets
      'support',                    // 使用 Google Sheets
      'blog',                       // 使用 Google Sheets + MDX (fs, path)
      'blog/[slug]',                // 使用 MDX (fs, path)
      'update-atm-order',           // 使用 Google Sheets
      'order/[orderNo]',            // 使用 Google Sheets
      'order-status',               // 使用 Google Sheets
      'admin/cache',                // 使用 Google Sheets (通過 cache-implementation)
      'webhook/payment',            // 使用 Google Sheets
      'pre-owned-watches',          // 使用 Google Sheets (通過 cache-implementation)
      'pre-owned-watches/[slug]',   // 使用 Google Sheets (通過 cache-implementation)
      'cache-performance',          // 使用 Google Sheets (通過 cache-implementation)
      'session-availability',       // 使用 Google Sheets
      'internal/google-sheets-operations', // 專門處理 Google Sheets 操作
    ];
  }

  async run() {
    console.log('🔍 開始分析 API routes 的 Edge Runtime 狀態...\n');

    try {
      await this.scanDirectory(this.apiDir);
      this.generateReport();
    } catch (error) {
      console.error('❌ 分析失敗:', error.message);
      process.exit(1);
    }
  }

  async scanDirectory(dir) {
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          await this.scanDirectory(fullPath);
        } else if (entry.name === 'route.ts' || entry.name === 'route.js') {
          await this.analyzeRouteFile(fullPath);
        }
      }
    } catch (error) {
      console.error(`❌ 無法讀取目錄 ${dir}:`, error.message);
    }
  }

  async analyzeRouteFile(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const routeName = this.getRouteNameFromPath(filePath);
      const relativePath = path.relative(path.join(__dirname, '..'), filePath);
      
      this.results.total++;

      // 檢查是否有 Edge Runtime 配置
      const hasEdgeRuntime = content.includes("runtime = 'edge'") || content.includes('runtime="edge"');
      
      // 檢查是否有 Node.js Runtime 配置
      const hasNodeRuntime = content.includes("runtime = 'nodejs'") || content.includes('runtime="nodejs"');

      // 檢查是否使用了 Node.js 特定的 API
      const nodeJsAPIs = this.detectNodeJsAPIs(content);
      
      // 檢查是否在黑名單中
      const isInBlacklist = this.nodeJsOnlyRoutes.includes(routeName);

      const routeInfo = {
        path: relativePath,
        routeName,
        hasEdgeRuntime,
        hasNodeRuntime,
        nodeJsAPIs,
        isInBlacklist,
        shouldUseEdge: !isInBlacklist && nodeJsAPIs.length === 0
      };

      if (hasEdgeRuntime) {
        this.results.edgeRuntime.push(routeInfo);
      } else if (hasNodeRuntime) {
        this.results.nodeRuntime.push(routeInfo);
      } else {
        this.results.noRuntimeSpecified.push(routeInfo);
      }

      if (isInBlacklist) {
        this.results.nodeJsOnlyRoutes.push(routeInfo);
      }

    } catch (error) {
      this.results.errors.push({
        path: filePath,
        error: error.message
      });
    }
  }

  getRouteNameFromPath(filePath) {
    const apiDir = path.join(__dirname, '../src/app/api');
    const routeDir = path.dirname(filePath);
    const routeName = path.relative(apiDir, routeDir).replace(/\\/g, '/');
    return routeName || 'root';
  }

  detectNodeJsAPIs(content) {
    const nodeJsPatterns = [
      /require\s*\(\s*['"]fs['"]\s*\)/,
      /require\s*\(\s*['"]path['"]\s*\)/,
      /require\s*\(\s*['"]os['"]\s*\)/,
      /require\s*\(\s*['"]crypto['"]\s*\)/,
      /require\s*\(\s*['"]buffer['"]\s*\)/,
      /import.*from\s+['"]fs['"]/,
      /import.*from\s+['"]path['"]/,
      /import.*from\s+['"]os['"]/,
      /import.*from\s+['"]crypto['"]/,
      /import.*from\s+['"]buffer['"]/,
      /process\.env/,
      /Buffer\./,
      /fs\./,
      /path\./,
      /__dirname/,
      /__filename/,
      /googleapis/,
      /google-auth-library/
    ];

    const detectedAPIs = [];
    for (const pattern of nodeJsPatterns) {
      if (pattern.test(content)) {
        detectedAPIs.push(pattern.source);
      }
    }

    return detectedAPIs;
  }

  generateReport() {
    console.log('📊 Edge Runtime 狀態分析報告');
    console.log('='.repeat(50));
    console.log(`總計 API routes: ${this.results.total}`);
    console.log(`已配置 Edge Runtime: ${this.results.edgeRuntime.length}`);
    console.log(`已配置 Node.js Runtime: ${this.results.nodeRuntime.length}`);
    console.log(`未指定 Runtime: ${this.results.noRuntimeSpecified.length}`);
    console.log(`Node.js 專用路由: ${this.results.nodeJsOnlyRoutes.length}`);
    console.log(`分析錯誤: ${this.results.errors.length}\n`);

    // 詳細報告
    if (this.results.edgeRuntime.length > 0) {
      console.log('✅ 已配置 Edge Runtime 的路由:');
      this.results.edgeRuntime.forEach(route => {
        console.log(`  - ${route.routeName} (${route.path})`);
      });
      console.log();
    }

    if (this.results.noRuntimeSpecified.length > 0) {
      console.log('⚠️  未指定 Runtime 的路由:');
      this.results.noRuntimeSpecified.forEach(route => {
        const status = route.shouldUseEdge ? '✅ 可轉換' : '❌ 需要 Node.js';
        console.log(`  - ${route.routeName} (${route.path}) - ${status}`);
        if (route.nodeJsAPIs.length > 0) {
          console.log(`    使用的 Node.js APIs: ${route.nodeJsAPIs.join(', ')}`);
        }
      });
      console.log();
    }

    if (this.results.nodeRuntime.length > 0) {
      console.log('🔧 已配置 Node.js Runtime 的路由:');
      this.results.nodeRuntime.forEach(route => {
        console.log(`  - ${route.routeName} (${route.path})`);
      });
      console.log();
    }

    if (this.results.nodeJsOnlyRoutes.length > 0) {
      console.log('🚫 Node.js 專用路由 (黑名單):');
      this.results.nodeJsOnlyRoutes.forEach(route => {
        console.log(`  - ${route.routeName} (${route.path})`);
      });
      console.log();
    }

    if (this.results.errors.length > 0) {
      console.log('❌ 分析錯誤:');
      this.results.errors.forEach(error => {
        console.log(`  - ${error.path}: ${error.error}`);
      });
      console.log();
    }

    // 建議
    console.log('💡 建議:');
    const canConvert = this.results.noRuntimeSpecified.filter(r => r.shouldUseEdge);
    if (canConvert.length > 0) {
      console.log(`  - 有 ${canConvert.length} 個路由可以安全轉換為 Edge Runtime`);
    }
    
    const needsNodeJs = this.results.noRuntimeSpecified.filter(r => !r.shouldUseEdge);
    if (needsNodeJs.length > 0) {
      console.log(`  - 有 ${needsNodeJs.length} 個路由需要保持 Node.js Runtime`);
    }

    // 儲存詳細報告到檔案
    this.saveDetailedReport();
  }

  async saveDetailedReport() {
    const reportPath = path.join(__dirname, '../runtime-analysis-report.json');
    try {
      await fs.writeFile(reportPath, JSON.stringify(this.results, null, 2));
      console.log(`📄 詳細報告已儲存至: ${reportPath}`);
    } catch (error) {
      console.error('❌ 無法儲存報告:', error.message);
    }
  }
}

// 執行分析
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new RuntimeAnalyzer();
  analyzer.run();
}

export default RuntimeAnalyzer;
