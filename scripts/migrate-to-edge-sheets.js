#!/usr/bin/env node

/**
 * Google Sheets API Edge Runtime 遷移腳本
 * 自動將現有的 Google Sheets API 調用遷移到 Edge Runtime 相容版本
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SheetsApiMigrator {
  constructor() {
    this.srcDir = path.join(__dirname, '../src');
    this.processedFiles = [];
    this.skippedFiles = [];
    this.errors = [];
    
    // 需要遷移的檔案模式
    this.targetPatterns = [
      /\/app\/api\/.*\/route\.ts$/,  // API 路由
      /\/lib\/.*\.ts$/,              // 工具庫
      /\/components\/.*\.tsx?$/      // 元件（如果有使用）
    ];

    // 需要替換的 import 語句
    this.importReplacements = [
      {
        from: "import { getSheetsClient, appendToSheet, getSheetData, getWatchSheetData, getBlogSheetData } from '@/lib/google-sheets';",
        to: "import { getSheetsClientEdge as getSheetsClient, appendToSheet, getSheetData, getWatchSheetData, getBlogSheetData } from '@/lib/google-sheets-migration';"
      },
      {
        from: "import { appendToSheet, getSheetData } from '@/lib/google-sheets';",
        to: "import { appendToSheet, getSheetData } from '@/lib/google-sheets-migration';"
      },
      {
        from: "import { getSheetData } from '@/lib/google-sheets';",
        to: "import { getSheetData } from '@/lib/google-sheets-migration';"
      },
      {
        from: "import { appendToSheet } from '@/lib/google-sheets';",
        to: "import { appendToSheet } from '@/lib/google-sheets-migration';"
      },
      {
        from: "import { getWatchSheetData } from '@/lib/google-sheets';",
        to: "import { getWatchSheetData } from '@/lib/google-sheets-migration';"
      },
      {
        from: "import { getBlogSheetData } from '@/lib/google-sheets';",
        to: "import { getBlogSheetData } from '@/lib/google-sheets-migration';"
      }
    ];

    // 需要添加 Edge Runtime 的 API 路由
    this.edgeRuntimeRoutes = [
      'contact',
      'event-registration', 
      'register',
      'pangea-appointment',
      'watch-appointment',
      'support',
      'blog',
      'update-atm-order',
      'order-status',
      'session-availability',
      'webhook/payment'
    ];
  }

  async run() {
    console.log('🚀 開始 Google Sheets API Edge Runtime 遷移...\n');

    try {
      await this.processDirectory(this.srcDir);
      this.printSummary();
    } catch (error) {
      console.error('❌ 遷移失敗:', error.message);
      process.exit(1);
    }
  }

  async processDirectory(dir) {
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          // 跳過 node_modules 和其他不需要的目錄
          if (!['node_modules', '.next', 'coverage', '.git'].includes(entry.name)) {
            await this.processDirectory(fullPath);
          }
        } else if (this.shouldProcessFile(fullPath)) {
          await this.processFile(fullPath);
        }
      }
    } catch (error) {
      console.error(`❌ 無法讀取目錄 ${dir}:`, error.message);
    }
  }

  shouldProcessFile(filePath) {
    return this.targetPatterns.some(pattern => pattern.test(filePath));
  }

  async processFile(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      let updatedContent = content;
      let hasChanges = false;

      // 檢查是否包含 Google Sheets 相關的 import
      const hasGoogleSheetsImport = content.includes("from '@/lib/google-sheets'");
      
      if (!hasGoogleSheetsImport) {
        console.log(`⏭️ 跳過 ${this.getRelativePath(filePath)} (無 Google Sheets 相關程式碼)`);
        this.skippedFiles.push(filePath);
        return;
      }

      // 替換 import 語句
      for (const replacement of this.importReplacements) {
        if (updatedContent.includes(replacement.from)) {
          updatedContent = updatedContent.replace(replacement.from, replacement.to);
          hasChanges = true;
          console.log(`🔄 更新 import: ${this.getRelativePath(filePath)}`);
        }
      }

      // 如果是 API 路由，添加 Edge Runtime 配置
      if (filePath.includes('/api/') && filePath.endsWith('/route.ts')) {
        const routeName = this.getRouteNameFromPath(filePath);
        
        if (this.edgeRuntimeRoutes.includes(routeName)) {
          const hasEdgeRuntime = updatedContent.includes("runtime = 'edge'") || updatedContent.includes('runtime="edge"');
          
          if (!hasEdgeRuntime) {
            updatedContent = this.addEdgeRuntime(updatedContent);
            hasChanges = true;
            console.log(`⚡ 添加 Edge Runtime: ${this.getRelativePath(filePath)}`);
          }
        }
      }

      // 移除舊的 Node.js Runtime 配置（如果存在）
      if (updatedContent.includes("runtime = 'nodejs'") || updatedContent.includes('runtime="nodejs"')) {
        updatedContent = this.removeNodejsRuntime(updatedContent);
        hasChanges = true;
        console.log(`🗑️ 移除 Node.js Runtime: ${this.getRelativePath(filePath)}`);
      }

      // 如果有變更，寫入檔案
      if (hasChanges) {
        await fs.writeFile(filePath, updatedContent, 'utf8');
        console.log(`✅ 已更新: ${this.getRelativePath(filePath)}`);
        this.processedFiles.push(filePath);
      } else {
        console.log(`⏭️ 跳過 ${this.getRelativePath(filePath)} (無需變更)`);
        this.skippedFiles.push(filePath);
      }

    } catch (error) {
      console.error(`❌ 處理檔案 ${filePath} 失敗:`, error.message);
      this.errors.push({ file: filePath, error: error.message });
    }
  }

  addEdgeRuntime(content) {
    // 尋找第一個 export function 或 export async function
    const exportFunctionRegex = /^export\s+(async\s+)?function\s+(GET|POST|PUT|DELETE|PATCH)/m;
    const match = content.match(exportFunctionRegex);
    
    if (match) {
      const insertPosition = match.index;
      
      // 在第一個 export function 前插入 Edge Runtime 配置
      const beforeFunction = content.substring(0, insertPosition);
      const afterFunction = content.substring(insertPosition);
      
      // 檢查前面是否已經有空行，如果沒有則添加
      const needsNewlineBefore = !beforeFunction.endsWith('\n\n');
      const edgeRuntimeConfig = `${needsNewlineBefore ? '\n' : ''}// Edge Runtime 配置 - Cloudflare Pages 相容\nexport const runtime = 'edge';\n\n`;
      
      return beforeFunction + edgeRuntimeConfig + afterFunction;
    }
    
    return content;
  }

  removeNodejsRuntime(content) {
    // 移除 Node.js Runtime 配置行
    const lines = content.split('\n');
    const filteredLines = lines.filter(line => {
      const trimmed = line.trim();
      return !(
        trimmed.includes("runtime = 'nodejs'") ||
        trimmed.includes('runtime="nodejs"') ||
        (trimmed.startsWith('//') && trimmed.includes('Node.js Runtime'))
      );
    });

    // 移除多餘的空行
    let result = filteredLines.join('\n');
    result = result.replace(/\n\n\n+/g, '\n\n'); // 將多個連續空行替換為兩個空行

    return result;
  }

  getRelativePath(filePath) {
    return path.relative(path.join(__dirname, '..'), filePath);
  }

  getRouteNameFromPath(filePath) {
    // 從路徑提取路由名稱
    const apiIndex = filePath.indexOf('/api/');
    if (apiIndex === -1) return '';
    
    const afterApi = filePath.substring(apiIndex + 5); // 跳過 '/api/'
    const routePath = afterApi.replace('/route.ts', '');
    
    return routePath;
  }

  printSummary() {
    console.log('\n📊 遷移結果摘要:');
    console.log(`✅ 已處理: ${this.processedFiles.length} 個檔案`);
    console.log(`⏭️ 已跳過: ${this.skippedFiles.length} 個檔案`);
    console.log(`❌ 錯誤: ${this.errors.length} 個檔案`);
    
    if (this.processedFiles.length > 0) {
      console.log('\n✅ 已處理的檔案:');
      this.processedFiles.forEach(file => {
        console.log(`  - ${this.getRelativePath(file)}`);
      });
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ 處理失敗的檔案:');
      this.errors.forEach(({ file, error }) => {
        console.log(`  - ${this.getRelativePath(file)}: ${error}`);
      });
    }
    
    console.log('\n🎉 Google Sheets API Edge Runtime 遷移完成！');
    console.log('\n📋 後續步驟:');
    console.log('1. 測試遷移後的 API 路由: /api/test-edge-sheets');
    console.log('2. 驗證所有表單提交功能正常運作');
    console.log('3. 檢查 Google Sheets 讀寫操作');
    console.log('4. 部署到 Cloudflare Pages 進行最終測試');
  }
}

// 執行遷移
const migrator = new SheetsApiMigrator();
migrator.run().catch(console.error);
