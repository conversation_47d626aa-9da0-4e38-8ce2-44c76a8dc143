#!/usr/bin/env tsx

/**
 * MDX 檔案建置時處理腳本
 * 將 content/blog/ 中的 MDX 檔案處理成 JSON 資料，供 Edge Runtime 使用
 */

import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

// 型別定義
interface BlogFrontmatter {
  title: string;
  publishDate: string;
  author: string;
  thumbnail: string;
  seoTitle: string;
  seoDescription: string;
  socialImage: string;
  socialTitle: string;
  socialDescription: string;
  slug: string;
  tags: string[];
  excerpt: string;
  featured: boolean;
}

interface ProcessedPost {
  slug: string;
  frontmatter: BlogFrontmatter;
  content: string;
  readingTime: number;
  filename: string;
}

interface BlogData {
  posts: ProcessedPost[];
  metadata: {
    totalPosts: number;
    lastUpdated: string;
    tags: string[];
    processedAt: string;
  };
  index: {
    bySlug: Record<string, number>;
    byTag: Record<string, number[]>;
    byDate: Record<string, number[]>;
  };
}

// 設定
const BLOG_CONTENT_DIR = path.join(process.cwd(), 'content', 'blog');
const OUTPUT_DIR = path.join(process.cwd(), 'src', 'data');
const OUTPUT_FILE = path.join(OUTPUT_DIR, 'blog.json');

/**
 * 從檔名提取 slug
 */
function extractSlugFromFilename(filename: string): string {
  const nameWithoutExt = filename.replace(/\.mdx$/, '');
  return nameWithoutExt.replace(/^\d{4}-\d{2}-\d{2}-/, '');
}

/**
 * 從檔名提取發布日期
 */
function extractDateFromFilename(filename: string): string {
  const match = filename.match(/^(\d{4}-\d{2}-\d{2})-/);
  return match ? match[1] : new Date().toISOString().split('T')[0];
}

/**
 * 計算閱讀時間（分鐘）
 */
function calculateReadingTime(content: string): number {
  const plainText = content
    .replace(/```[\s\S]*?```/g, '') // 移除程式碼區塊
    .replace(/`[^`]*`/g, '') // 移除行內程式碼
    .replace(/!\[.*?\]\(.*?\)/g, '') // 移除圖片
    .replace(/\[.*?\]\(.*?\)/g, '') // 移除連結
    .replace(/<[^>]*>/g, '') // 移除 HTML 標籤
    .replace(/[#*_~`]/g, '') // 移除 Markdown 語法
    .trim();
  
  const chineseChars = (plainText.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = (plainText.match(/[a-zA-Z]+/g) || []).length;
  
  const readingTimeMinutes = Math.ceil((chineseChars / 300) + (englishWords / 200));
  return Math.max(1, readingTimeMinutes);
}

/**
 * 處理單個 MDX 檔案
 */
function processMdxFile(filename: string): ProcessedPost | null {
  try {
    const filePath = path.join(BLOG_CONTENT_DIR, filename);
    
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️ 檔案不存在: ${filename}`);
      return null;
    }
    
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const { data, content } = matter(fileContent);
    
    // 驗證必要的 frontmatter 欄位
    if (!data.title || !data.slug) {
      console.warn(`⚠️ MDX 檔案 ${filename} 缺少必要的 frontmatter 欄位`);
      return null;
    }
    
    const frontmatter: BlogFrontmatter = {
      title: data.title,
      publishDate: data.publishDate || extractDateFromFilename(filename),
      author: data.author || 'Weaven',
      thumbnail: data.thumbnail || '',
      seoTitle: data.seoTitle || data.title,
      seoDescription: data.seoDescription || '',
      socialImage: data.socialImage || data.thumbnail || '',
      socialTitle: data.socialTitle || data.title,
      socialDescription: data.socialDescription || data.seoDescription || '',
      slug: data.slug || extractSlugFromFilename(filename),
      tags: data.tags || [],
      excerpt: data.excerpt || '',
      featured: data.featured || false,
    };
    
    return {
      slug: frontmatter.slug,
      frontmatter,
      content,
      readingTime: calculateReadingTime(content),
      filename,
    };
  } catch (error) {
    console.error(`❌ 處理 MDX 檔案 ${filename} 失敗:`, error);
    return null;
  }
}

/**
 * 建立索引
 */
function createIndexes(posts: ProcessedPost[]): BlogData['index'] {
  const bySlug: Record<string, number> = {};
  const byTag: Record<string, number[]> = {};
  const byDate: Record<string, number[]> = {};
  
  posts.forEach((post, index) => {
    // Slug 索引
    bySlug[post.slug] = index;
    
    // 標籤索引
    post.frontmatter.tags.forEach(tag => {
      if (!byTag[tag]) byTag[tag] = [];
      byTag[tag].push(index);
    });
    
    // 日期索引（按年月分組）
    const dateKey = post.frontmatter.publishDate.substring(0, 7); // YYYY-MM
    if (!byDate[dateKey]) byDate[dateKey] = [];
    byDate[dateKey].push(index);
  });
  
  return { bySlug, byTag, byDate };
}

/**
 * 主要處理函數
 */
function processMdxFiles(): BlogData {
  console.log('🚀 開始處理 MDX 檔案...');
  
  // 檢查目錄是否存在
  if (!fs.existsSync(BLOG_CONTENT_DIR)) {
    console.warn(`⚠️ 部落格內容目錄不存在: ${BLOG_CONTENT_DIR}`);
    return {
      posts: [],
      metadata: {
        totalPosts: 0,
        lastUpdated: new Date().toISOString(),
        tags: [],
        processedAt: new Date().toISOString(),
      },
      index: { bySlug: {}, byTag: {}, byDate: {} },
    };
  }
  
  // 獲取所有 MDX 檔案
  const files = fs
    .readdirSync(BLOG_CONTENT_DIR)
    .filter(file => file.endsWith('.mdx') && file !== 'README.md')
    .sort((a, b) => b.localeCompare(a)); // 按檔名降序排列
  
  console.log(`📁 找到 ${files.length} 個 MDX 檔案`);
  
  // 處理每個檔案
  const posts: ProcessedPost[] = [];
  for (const filename of files) {
    console.log(`📄 處理: ${filename}`);
    const post = processMdxFile(filename);
    if (post) {
      posts.push(post);
    }
  }
  
  // 按發布日期降序排列
  posts.sort((a, b) => 
    new Date(b.frontmatter.publishDate).getTime() - 
    new Date(a.frontmatter.publishDate).getTime()
  );
  
  // 收集所有標籤
  const allTags = new Set<string>();
  posts.forEach(post => {
    post.frontmatter.tags.forEach(tag => allTags.add(tag));
  });
  
  // 建立索引
  const index = createIndexes(posts);
  
  const blogData: BlogData = {
    posts,
    metadata: {
      totalPosts: posts.length,
      lastUpdated: new Date().toISOString(),
      tags: Array.from(allTags).sort(),
      processedAt: new Date().toISOString(),
    },
    index,
  };
  
  console.log(`✅ 成功處理 ${posts.length} 篇文章`);
  console.log(`🏷️ 找到 ${allTags.size} 個標籤`);
  
  return blogData;
}

/**
 * 儲存處理結果
 */
function saveBlogData(blogData: BlogData): void {
  // 確保輸出目錄存在
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }
  
  // 寫入 JSON 檔案
  fs.writeFileSync(OUTPUT_FILE, JSON.stringify(blogData, null, 2), 'utf8');
  
  const fileSize = (fs.statSync(OUTPUT_FILE).size / 1024).toFixed(2);
  console.log(`💾 已儲存到: ${OUTPUT_FILE} (${fileSize} KB)`);
}

/**
 * 主程式
 */
function main(): void {
  try {
    console.log('🔄 MDX 檔案處理開始...');
    
    const startTime = Date.now();
    const blogData = processMdxFiles();
    saveBlogData(blogData);
    const endTime = Date.now();
    
    console.log(`⏱️ 處理完成，耗時: ${endTime - startTime}ms`);
    console.log('🎉 MDX 檔案處理完成！');
    
  } catch (error) {
    console.error('❌ MDX 檔案處理失敗:', error);
    process.exit(1);
  }
}

// 執行主程式
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { processMdxFiles };
export type { BlogData, ProcessedPost, BlogFrontmatter };
