/**
 * PayUni 付款整合 API 測試
 * 測試 /api/payment/create, /api/payment/callback, /api/payment/query 等付款相關端點
 */

// 暫時禁用 MSW，專注於基本測試功能
// import { server } from '../mocks/server';
import { setupTestEnvironment, createTestApiRequest, parseResponse, generateTestEmail, generateTestOrderNo } from '../utils/test-helpers';
import { paymentTestData, orderStatusTestData } from '../fixtures/form-data';

// 導入 PayUni 工具函數進行單元測試
import { createPaymentRequestEdge, convertTradeStatus, convertPaymentType, calculateATMExpireDateEdge } from '@/lib/payuni-edge';

describe('PayUni 付款整合 API 測試', () => {
  let restoreEnv: () => void;

  beforeAll(() => {
    // server.listen();
    restoreEnv = setupTestEnvironment();
  });

  afterEach(() => {
    // server.resetHandlers();
  });

  afterAll(() => {
    // server.close();
    restoreEnv();
  });

  describe('PayUni 工具函數測試', () => {
    test('應該正確轉換付款狀態', () => {
      expect(convertTradeStatus('1')).toBe('已付款');
      expect(convertTradeStatus('2')).toBe('付款失敗');
      expect(convertTradeStatus('9')).toBe('未付款');
      expect(convertTradeStatus('unknown')).toBe('未知狀態');
    });

    test('應該正確轉換付款方式', () => {
      expect(convertPaymentType('1')).toBe('信用卡');
      expect(convertPaymentType('2')).toBe('ATM轉帳');
      expect(convertPaymentType('unknown')).toBe('未知支付方式');
    });

    test('應該正確計算 ATM 轉帳到期日期', () => {
      const expireDate = calculateATMExpireDate();

      // 驗證日期格式 YYYY-MM-DD
      expect(expireDate).toMatch(/^\d{4}-\d{2}-\d{2}$/);

      // 驗證日期是未來的日期
      const today = new Date();
      const expire = new Date(expireDate);
      expect(expire.getTime()).toBeGreaterThan(today.getTime());
    });

    test('應該根據台灣時間正確計算 ATM 到期日期', () => {
      const expireDate = calculateATMExpireDate();

      // 取得當前台灣時間
      const now = new Date();
      const taiwanTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
      const taiwanHour = taiwanTime.getUTCHours();

      // 計算預期的到期日期
      const expectedDays = taiwanHour >= 14 ? 3 : 2;
      const expectedExpireDate = new Date();
      expectedExpireDate.setDate(expectedExpireDate.getDate() + expectedDays);
      const expectedDateString = expectedExpireDate.toISOString().split('T')[0];

      expect(expireDate).toBe(expectedDateString);
    });

    test('應該正確驗證付款資料格式', () => {
      const validPaymentData = {
        MerTradeNo: generateTestOrderNo(),
        TradeAmt: 3000,
        ProdDesc: '錶匠體驗機芯拆解',
        UsrMail: generateTestEmail()
      };

      // 驗證必要欄位存在
      expect(validPaymentData.MerTradeNo).toBeTruthy();
      expect(validPaymentData.TradeAmt).toBeGreaterThan(0);
      expect(validPaymentData.ProdDesc).toBeTruthy();
      expect(validPaymentData.UsrMail).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
    });

    test('應該正確處理訂單編號格式', () => {
      const orderNo1 = generateTestOrderNo();
      const orderNo2 = generateTestOrderNo();

      // 確保訂單編號格式正確
      expect(orderNo1).toMatch(/^pangea_test_\d+_[a-z0-9]+$/);
      expect(orderNo2).toMatch(/^pangea_test_\d+_[a-z0-9]+$/);

      // 確保訂單編號唯一
      expect(orderNo1).not.toBe(orderNo2);

      // 確保長度在合理範圍內（PayUni 有長度限制）
      expect(orderNo1.length).toBeLessThan(50);
      expect(orderNo2.length).toBeLessThan(50);
    });
  });

  describe('PayUni 回調資料處理', () => {
    test('應該正確處理成功的回調資料', () => {
      const successCallbackData = {
        Status: 'SUCCESS',
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'payuni_test_456',
        TradeAmt: '3000',
        PaymentType: '1',
        TradeStatus: '1',
        PaymentDay: '2025-01-01 12:00:00'
      };

      expect(successCallbackData.Status).toBe('SUCCESS');
      expect(successCallbackData.TradeStatus).toBe('1');
      expect(convertTradeStatus(successCallbackData.TradeStatus)).toBe('已付款');
      expect(convertPaymentType(successCallbackData.PaymentType)).toBe('信用卡');
    });

    test('應該正確處理失敗的回調資料', () => {
      const failedCallbackData = {
        Status: 'FAIL',
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'payuni_test_456',
        TradeAmt: '3000',
        PaymentType: '1',
        TradeStatus: '2', // 付款失敗
        Message: '付款失敗'
      };

      expect(failedCallbackData.Status).toBe('FAIL');
      expect(failedCallbackData.TradeStatus).toBe('2');
      expect(convertTradeStatus(failedCallbackData.TradeStatus)).toBe('付款失敗');
      expect(failedCallbackData.Message).toBe('付款失敗');
    });
  });

  describe('訂單查詢資料驗證', () => {
    test('應該正確驗證訂單查詢參數', () => {
      const validOrderQuery = {
        orderNo: 'pangea_test_123',
        email: '<EMAIL>'
      };

      expect(validOrderQuery.orderNo).toMatch(/^pangea_/);
      expect(validOrderQuery.email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
      expect(validOrderQuery.orderNo.length).toBeGreaterThan(0);
      expect(validOrderQuery.email.length).toBeGreaterThan(0);
    });

    test('應該正確處理訂單狀態資料', () => {
      const orderStatusData = {
        orderNo: 'pangea_test_123',
        paymentStatus: '已付款',
        paymentMethod: '信用卡',
        tradeStatus: '1',
        paymentCompletedAt: '2025-01-01T12:00:00.000Z'
      };

      expect(orderStatusData.orderNo).toBeTruthy();
      expect(orderStatusData.paymentStatus).toBe('已付款');
      expect(orderStatusData.paymentMethod).toBe('信用卡');
      expect(convertTradeStatus(orderStatusData.tradeStatus)).toBe('已付款');
      expect(new Date(orderStatusData.paymentCompletedAt)).toBeInstanceOf(Date);
    });
  });

  describe('重新付款資料處理', () => {
    test('應該正確處理重新付款請求資料', () => {
      const retryData = {
        originalOrderNo: 'pangea_test_123',
        email: generateTestEmail(),
        newOrderNo: generateTestOrderNo()
      };

      expect(retryData.originalOrderNo).toMatch(/^pangea_/);
      expect(retryData.email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
      expect(retryData.newOrderNo).toMatch(/^pangea_test_/);
      expect(retryData.newOrderNo).not.toBe(retryData.originalOrderNo);
    });

    test('應該正確驗證重新付款資料格式', () => {
      const invalidRetryData = {
        originalOrderNo: '',
        email: 'invalid-email'
      };

      expect(invalidRetryData.originalOrderNo).toBe('');
      expect(invalidRetryData.email).not.toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
    });
  });

  describe('Webhook 資料處理', () => {
    test('應該正確處理 webhook 通知資料', () => {
      const webhookData = {
        Status: 'SUCCESS',
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'payuni_test_456',
        TradeAmt: '3000',
        PaymentType: '1',
        TradeStatus: '1',
        PaymentDay: '2025-01-01 12:00:00'
      };

      expect(webhookData.Status).toBe('SUCCESS');
      expect(webhookData.MerTradeNo).toMatch(/^pangea_/);
      expect(parseInt(webhookData.TradeAmt)).toBeGreaterThan(0);
      expect(convertTradeStatus(webhookData.TradeStatus)).toBe('已付款');
      expect(convertPaymentType(webhookData.PaymentType)).toBe('信用卡');
    });

    test('應該正確處理退款通知資料', () => {
      const refundWebhookData = {
        Status: 'SUCCESS',
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'payuni_test_456',
        TradeAmt: '3000',
        PaymentType: '1',
        TradeStatus: '1',
        RefundStatus: '2', // 已退款
        RefundAmt: '3000',
        PaymentDay: '2025-01-01 12:00:00'
      };

      expect(refundWebhookData.RefundStatus).toBe('2');
      expect(parseInt(refundWebhookData.RefundAmt)).toBe(parseInt(refundWebhookData.TradeAmt));
      expect(refundWebhookData.Status).toBe('SUCCESS');
    });
  });

  describe('錯誤處理資料驗證', () => {
    test('應該正確識別錯誤回應格式', () => {
      const errorResponse = {
        Status: 'FAIL',
        Message: 'UPP02003 - 訂單編號長度超過限制',
        ErrorCode: 'UPP02003'
      };

      expect(errorResponse.Status).toBe('FAIL');
      expect(errorResponse.Message).toContain('UPP02003');
      expect(errorResponse.ErrorCode).toBe('UPP02003');
    });

    test('應該正確處理超時錯誤', () => {
      const timeoutError = {
        Status: 'TIMEOUT',
        Message: '請求超時',
        Code: 408
      };

      expect(timeoutError.Status).toBe('TIMEOUT');
      expect(timeoutError.Code).toBe(408);
      expect(timeoutError.Message).toContain('超時');
    });

    test('應該正確驗證訂單編號長度限制', () => {
      const longOrderNo = 'pangea_' + 'a'.repeat(100);
      const normalOrderNo = generateTestOrderNo();

      expect(longOrderNo.length).toBeGreaterThan(50);
      expect(normalOrderNo.length).toBeLessThan(50);
    });
  });
});
