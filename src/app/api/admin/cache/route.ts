import { NextRequest, NextResponse } from 'next/server';
import {
  getCacheStats,
  clearCache,
  warmupCache
} from '@/lib/cache-implementation';

// 檢查管理員權限
function checkAdminAuth(request: NextRequest): boolean {
  // 在開發環境中允許所有請求
  if (process.env.NODE_ENV === 'development') {
    return true;
  }
  
  // 在生產環境中檢查認證
  const authHeader = request.headers.get('authorization');
  const adminToken = process.env.ADMIN_TOKEN;
  
  if (!adminToken || !authHeader) {
    return false;
  }
  
  return authHeader === `Bearer ${adminToken}`;
}

/**
 * GET /api/admin/cache
 * 獲取快取統計和健康狀態
 */

export async function GET(request: NextRequest) {
  try {
    // 檢查權限
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: '未授權存取' },
        { status: 401 }
      );
    }

    const stats = getCacheStats();
    return NextResponse.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('獲取快取資訊失敗:', error);
    return NextResponse.json(
      {
        success: false,
        error: '獲取快取資訊失敗',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/cache
 * 執行快取操作（預熱等）
 */
export async function POST(request: NextRequest) {
  try {
    // 檢查權限
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: '未授權存取' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'warmup':
        await warmupCache();
        return NextResponse.json({
          success: true,
          message: '快取預熱成功'
        });

      default:
        return NextResponse.json(
          { error: `不支援的操作: ${action}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('執行快取操作失敗:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '執行快取操作失敗',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/cache
 * 清除快取
 */
export async function DELETE(request: NextRequest) {
  try {
    // 檢查權限
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: '未授權存取' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    if (type) {
      clearCache(type);
      return NextResponse.json({
        success: true,
        message: `清除 ${type} 快取成功`
      });
    } else {
      clearCache();
      return NextResponse.json({
        success: true,
        message: '清除所有快取成功'
      });
    }
  } catch (error) {
    console.error('清除快取失敗:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '清除快取失敗',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/cache
 * 更新快取設定
 */
export async function PUT(request: NextRequest) {
  try {
    // 檢查權限
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: '未授權存取' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'set-ttl':
        // 目前的快取實作不支援動態 TTL 設定
        // 這個功能需要重啟應用才能生效
        return NextResponse.json({
          success: false,
          message: '動態 TTL 設定功能尚未實作，請透過環境變數 CACHE_TTL 設定'
        });

      case 'set-logging':
        // 目前的快取實作不支援動態日誌設定
        // 這個功能需要重啟應用才能生效
        return NextResponse.json({
          success: false,
          message: '動態日誌設定功能尚未實作，請透過環境變數 CACHE_LOGGING 設定'
        });

      default:
        return NextResponse.json(
          { error: `不支援的操作: ${action}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('更新快取設定失敗:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '更新快取設定失敗',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/admin/cache
 * CORS 預檢請求
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
