import { NextRequest, NextResponse } from 'next/server';
import { cachePerformanceMonitor } from '@/lib/cache-performance-monitor';
import {
  getCachedWatchListData,
  getCachedFAQData
} from '@/lib/cache-implementation';
import { getAllMdxPosts } from '@/lib/blog-data-edge';

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

/**
 * GET /api/cache-performance
 * 獲取快取效能報告
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'report':
        const report = cachePerformanceMonitor.getPerformanceReport();
        return NextResponse.json({
          success: true,
          data: report,
          timestamp: new Date().toISOString()
        });

      case 'metrics':
        const metrics = cachePerformanceMonitor.getMetrics();
        return NextResponse.json({
          success: true,
          data: metrics,
          timestamp: new Date().toISOString()
        });

      case 'test':
        // 執行快取效能測試
        const testResults = await performCacheTest();
        return NextResponse.json({
          success: true,
          data: testResults,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json({
          success: true,
          data: {
            availableActions: ['report', 'metrics', 'test'],
            description: '快取效能監控 API'
          }
        });
    }
  } catch (error) {
    console.error('快取效能 API 錯誤:', error);
    return NextResponse.json(
      {
        success: false,
        error: '獲取快取效能資料失敗',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/cache-performance
 * 執行快取操作和測試
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, iterations = 5 } = body;

    switch (action) {
      case 'benchmark':
        const benchmarkResults = await runCacheBenchmark(iterations);
        return NextResponse.json({
          success: true,
          data: benchmarkResults,
          timestamp: new Date().toISOString()
        });

      case 'reset':
        cachePerformanceMonitor.clearAll();
        return NextResponse.json({
          success: true,
          message: '快取效能監控資料已重置'
        });

      case 'warmup-test':
        const warmupResults = await testCacheWarmup();
        return NextResponse.json({
          success: true,
          data: warmupResults,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { error: `不支援的操作: ${action}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('快取效能操作錯誤:', error);
    return NextResponse.json(
      {
        success: false,
        error: '執行快取效能操作失敗',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * 執行快取效能測試
 */
async function performCacheTest() {
  interface TestResult {
    dataSource: string;
    firstLoadTime: number;
    cachedLoadTime: number;
    speedupRatio: number;
    improvement: number;
  }

  const results = {
    testStartTime: new Date(),
    tests: [] as TestResult[],
    summary: {
      totalTests: 0,
      averageFirstLoadTime: 0,
      averageCachedLoadTime: 0,
      cacheSpeedupRatio: 0
    }
  };

  // 測試部落格資料（Edge Runtime 預處理資料）
  const blogTest = await testDataSource('blog', getAllMdxPosts);
  results.tests.push(blogTest);

  // 測試手錶資料
  const watchTest = await testDataSource('watch', getCachedWatchListData);
  results.tests.push(watchTest);

  // 測試 FAQ 資料
  const faqTest = await testDataSource('faq', getCachedFAQData);
  results.tests.push(faqTest);

  // 計算總結
  results.summary.totalTests = results.tests.length;
  results.summary.averageFirstLoadTime = 
    results.tests.reduce((sum, test) => sum + test.firstLoadTime, 0) / results.tests.length;
  results.summary.averageCachedLoadTime = 
    results.tests.reduce((sum, test) => sum + test.cachedLoadTime, 0) / results.tests.length;
  results.summary.cacheSpeedupRatio = 
    results.summary.averageFirstLoadTime / results.summary.averageCachedLoadTime;

  return results;
}

/**
 * 測試單一資料源的快取效能
 */
async function testDataSource(name: string, fetchFunction: () => Promise<unknown>) {
  // 第一次載入（快取未命中）
  const firstLoadStart = performance.now();
  await fetchFunction();
  const firstLoadTime = performance.now() - firstLoadStart;

  // 第二次載入（快取命中）
  const cachedLoadStart = performance.now();
  await fetchFunction();
  const cachedLoadTime = performance.now() - cachedLoadStart;

  return {
    dataSource: name,
    firstLoadTime: Math.round(firstLoadTime * 100) / 100,
    cachedLoadTime: Math.round(cachedLoadTime * 100) / 100,
    speedupRatio: Math.round((firstLoadTime / cachedLoadTime) * 100) / 100,
    improvement: Math.round(((firstLoadTime - cachedLoadTime) / firstLoadTime) * 10000) / 100
  };
}

/**
 * 執行快取基準測試
 */
async function runCacheBenchmark(iterations: number) {
  interface BenchmarkTest {
    iteration: number;
    totalTests: number;
    averageFirstLoadTime: number;
    averageCachedLoadTime: number;
    cacheSpeedupRatio: number;
  }

  const results = {
    iterations,
    startTime: new Date(),
    tests: [] as BenchmarkTest[],
    summary: {
      averageSpeedup: 0,
      totalRequests: 0,
      cacheHitRate: 0
    }
  };

  for (let i = 0; i < iterations; i++) {
    const iterationResult = await performCacheTest();
    results.tests.push({
      iteration: i + 1,
      ...iterationResult.summary
    });
  }

  // 計算平均值
  results.summary.averageSpeedup = 
    results.tests.reduce((sum, test) => sum + test.cacheSpeedupRatio, 0) / iterations;
  
  const finalMetrics = cachePerformanceMonitor.getMetrics();
  results.summary.totalRequests = finalMetrics.totalRequests;
  results.summary.cacheHitRate = finalMetrics.cacheHitRate;

  return results;
}

/**
 * 測試快取預熱效果
 */
async function testCacheWarmup() {
  const { performCacheWarmup } = await import('@/lib/cache-warmup');
  
  const warmupStart = performance.now();
  await performCacheWarmup();
  const warmupTime = performance.now() - warmupStart;

  // 測試預熱後的載入速度
  const postWarmupTest = await performCacheTest();

  return {
    warmupTime: Math.round(warmupTime * 100) / 100,
    postWarmupPerformance: postWarmupTest,
    recommendation: warmupTime < 1000 ? 
      '預熱速度良好，建議啟用自動預熱' : 
      '預熱時間較長，建議檢查資料源效能'
  };
}
