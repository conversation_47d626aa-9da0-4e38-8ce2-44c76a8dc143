import { NextRequest, NextResponse } from 'next/server';
import { getSheetsClientEdge as getSheetsClient } from '@/lib/google-sheets-migration';
import { createTrackingManager } from '@/lib/tracking-manager';
import { validateSubmission, type SubmissionData } from '@/lib/anti-bot';
import { checkRateLimit, recordRequest, getClientIP, checkEmailRateLimit, recordEmailRequest } from '@/lib/rate-limiter';

/**
 * 取得 UTC+8 時區的時間字串
 */
function getUTC8TimeString(): string {
  const now = new Date();
  const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
  return utc8Time.toISOString().replace('Z', '+08:00');
}

interface ContactData {
  // 基本資訊
  name: string;
  email: string;
  phone?: string;
  contactMethod: 'email' | 'phone';
  
  // 諮詢內容
  topic: string;
  message: string;
  
  // 提交時間
  submittedAt: string;
  
  // UTM 追蹤參數
  utmParams?: {
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_term?: string;
    utm_content?: string;
    fbclid?: string;
  };
  
  // 安全驗證資料
  security?: {
    honeypotValue: string;
    formStartTime: number;
    securityToken: string;
    submissionTime: number;
    userAgent: string;
  };
}

// Edge Runtime 配置 - Cloudflare Pages 相容
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    const data: ContactData = await request.json();

    console.log('📝 收到聯絡表單請求:', {
      name: data.name,
      email: data.email,
      topic: data.topic,
      contactMethod: data.contactMethod,
      utmParams: data.utmParams
    });

    // 記錄 UTM 參數
    if (data.utmParams) {
      console.log('🔗 UTM 追蹤參數:', data.utmParams);
    }

    // === 安全驗證 ===
    const clientIP = getClientIP(request);
    console.log('🔒 安全驗證開始:', { ip: clientIP, email: data.email });

    // 1. IP 速率限制檢查
    const ipRateLimit = checkRateLimit(`ip:${clientIP}`);
    if (!ipRateLimit.allowed) {
      console.warn('🚫 IP 速率限制觸發:', { ip: clientIP, hits: ipRateLimit.totalHits });
      recordRequest(`ip:${clientIP}`, false);
      return NextResponse.json(
        { error: '請求過於頻繁，請稍後再試' },
        { status: 429 }
      );
    }

    // 2. Email 速率限制檢查
    const emailRateLimit = checkEmailRateLimit(data.email);
    if (!emailRateLimit.allowed) {
      console.warn('🚫 Email 速率限制觸發:', { email: data.email, hits: emailRateLimit.totalHits });
      recordRequest(`ip:${clientIP}`, false);
      recordEmailRequest(data.email, false);
      return NextResponse.json(
        { error: '此 Email 地址提交過於頻繁，請稍後再試' },
        { status: 429 }
      );
    }

    // 3. 安全資料驗證
    if (data.security) {
      const submissionData: SubmissionData = {
        email: data.email,
        formStartTime: data.security.formStartTime,
        submissionTime: data.security.submissionTime,
        honeypotValue: data.security.honeypotValue,
        userAgent: data.security.userAgent,
        ip: clientIP,
      };

      const securityValidation = validateSubmission(submissionData);
      if (!securityValidation.isValid) {
        console.warn('🚫 安全驗證失敗:', {
          reason: securityValidation.reason,
          riskScore: securityValidation.riskScore,
          email: data.email,
          ip: clientIP
        });
        recordRequest(`ip:${clientIP}`, false);
        recordEmailRequest(data.email, false);
        return NextResponse.json(
          { error: '表單驗證失敗，請重新整理頁面後再試' },
          { status: 400 }
        );
      }
      console.log('✅ 安全驗證通過:', { email: data.email, riskScore: securityValidation.riskScore });
    }

    // === 準備寫入 Google Sheets 的資料 ===
    // 欄位順序：Submitted at, 姓名, Email, 手機, 如何聯繫, 諮詢主題, 內容
    const sheetData = [
      getUTC8TimeString(), // A: Submitted at
      data.name, // B: 姓名
      data.email || '', // C: Email
      data.phone || '', // D: 手機
      data.contactMethod === 'email' ? 'Email' : '電話', // E: 如何聯繫
      data.topic, // F: 諮詢主題
      data.message, // G: 內容
    ];

    // 寫入 Google Sheets
    const contactSheetId = process.env.GOOGLE_SANDBOX_CONTACT_SHEET_ID;
    if (!contactSheetId) {
      throw new Error('GOOGLE_SANDBOX_CONTACT_SHEET_ID 環境變數未設定');
    }

    try {
      const sheets = getSheetsClient();
      await sheets.appendValues(contactSheetId, '工作表1!A:G', [sheetData]);
      console.log('✅ 聯絡表單資料已寫入 Google Sheets');
    } catch (error) {
      console.error('❌ 寫入 Google Sheets 失敗:', error);
      throw new Error('無法寫入聯絡表單資料到 Google Sheets');
    }

    // === 追蹤事件 ===
    try {
      const trackingManager = createTrackingManager(request);

      // 發送 Lead 事件到 CAPI
      await trackingManager.trackLead({
        formName: 'contact_form',
        value: 1,
        currency: 'TWD',
        email: data.email,
        phone: data.phone,
        userId: data.email,
        category: 'Contact',
        customData: {
          contact_method: data.contactMethod,
          topic: data.topic,
          form_type: 'contact_form'
        }
      });

      console.log('✅ Lead 追蹤事件已發送');
    } catch (trackingError) {
      console.error('❌ 追蹤事件發送失敗:', trackingError);
      // 不影響主要流程，繼續執行
    }

    // 記錄成功的請求
    recordRequest(`ip:${clientIP}`, true);
    recordEmailRequest(data.email, true);
    console.log('✅ 聯絡表單處理完成:', { email: data.email });

    return NextResponse.json({
      success: true,
      message: '感謝您的聯絡！我們會在 48 小時內回覆您。'
    });

  } catch (error) {
    console.error('處理聯絡表單請求時發生錯誤:', error);

    // 記錄失敗的請求（如果有 IP 資訊）
    try {
      const clientIP = getClientIP(request);
      recordRequest(`ip:${clientIP}`, false);
    } catch (ipError) {
      console.error('無法記錄失敗請求:', ipError);
    }

    return NextResponse.json(
      { error: '提交失敗，請稍後再試' },
      { status: 500 }
    );
  }
}
