import { NextResponse } from 'next/server';
import { createPaymentRequestEdge, calculateATMExpireDateEdge } from '@/lib/payuni-edge';

// 使用 Edge Runtime + Web Crypto API
export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { orderNo, eventName, eventPrice, userName, userEmail } = body;

    // 驗證必要欄位
    if (!orderNo || !eventName || !eventPrice || !userName || !userEmail) {
      return NextResponse.json(
        { error: '缺少必要欄位' },
        { status: 400 }
      );
    }

    // Version 參數需要單獨傳送，不包含在加密資料中
    const version = '1.0';

    // 計算 ATM 轉帳動態到期日期（基於台灣時間 14:00 判斷）
    const expireDate = calculateATMExpireDateEdge();

    // 建立 PayUni 付款請求所需的資料（根據官方文件規格）
    console.log('🕐 時間戳記資訊:', {
      currentTime: new Date().toISOString(),
      expireDate
    });

    // 產生加密後的付款參數
    const paymentRequest = await createPaymentRequestEdge({
      orderNo,
      eventName,
      eventPrice,
      userName,
      userEmail,
      expireDate
    });

    return NextResponse.json({
      success: true,
      MerID: paymentRequest.MerID,
      EncryptInfo: paymentRequest.EncryptInfo,
      HashInfo: paymentRequest.HashInfo,
      Version: version,
      ApiUrl: paymentRequest.ApiUrl
    });

  } catch (error) {
    console.error('建立付款請求失敗:', error);
    return NextResponse.json(
      { error: '建立付款請求失敗' },
      { status: 500 }
    );
  }
}
