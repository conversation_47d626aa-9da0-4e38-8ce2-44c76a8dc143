import { NextRequest, NextResponse } from 'next/server';
import { decryptPayUniDataEdge } from '@/lib/payuni-edge';
import { PAYUNI_CONFIG } from '@/config/environment-config';

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    const { encryptInfo, hashInfo } = await request.json();

    if (!encryptInfo || !hashInfo) {
      return NextResponse.json({ error: '缺少 EncryptInfo 或 HashInfo 參數' }, { status: 400 });
    }

    const hashKey = PAYUNI_CONFIG.getHashKey();
    const hashIV = PAYUNI_CONFIG.getHashIV();

    if (!hashKey || !hashIV) {
      return NextResponse.json({
        error: `PayUni 憑證未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`
      }, { status: 500 });
    }

    console.log('開始解密 PayUni 資料:', {
      encryptInfoLength: encryptInfo.length,
      environment: PAYUNI_CONFIG.ENVIRONMENT,
      encryptInfoPreview: encryptInfo.substring(0, 50) + '...'
    });

    // 解密 PayUni 資料
    const decryptedData = await decryptPayUniDataEdge(encryptInfo, hashInfo);

    console.log('PayUni 解密成功:', decryptedData);

    return NextResponse.json({
      success: true,
      data: decryptedData
    });

  } catch (error) {
    console.error('解密失敗:', error);
    return NextResponse.json({
      error: '解密失敗',
      details: error instanceof Error ? error.message : '未知錯誤'
    }, { status: 500 });
  }
}
