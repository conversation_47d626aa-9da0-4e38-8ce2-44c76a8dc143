import { NextRequest, NextResponse } from 'next/server';
import { getSheetData, getSheetsClientEdge as getSheetsClient } from '@/lib/google-sheets-migration';
import { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

/**
 * 內部 Google Sheets 操作 API
 * 專門處理需要 Google Sheets API 的操作，供 Edge Runtime API 調用
 *
 * 使用 Edge Runtime 相容的 Google Sheets 實作
 */

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { operation, params } = body;

    console.log('🔄 收到內部 Google Sheets 操作請求:', { operation, params });

    // 驗證是否為內部調用
    const userAgent = request.headers.get('user-agent');
    if (!userAgent?.includes('internal-sheets-operation')) {
      return NextResponse.json(
        { error: '此 API 僅供內部調用' },
        { status: 403 }
      );
    }

    switch (operation) {
      case 'queryOrder':
        return await handleQueryOrder(params);
      
      case 'updateRetryPayment':
        return await handleUpdateRetryPayment(params);
      
      default:
        return NextResponse.json(
          { error: `不支援的操作: ${operation}` },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ 內部 Google Sheets 操作失敗:', error);
    return NextResponse.json(
      { error: '內部操作失敗' },
      { status: 500 }
    );
  }
}

/**
 * 查詢訂單資料
 */
async function handleQueryOrder(params: { orderNo: string }) {
  const { orderNo } = params;
  const sheetName = '工作表1';

  console.log(`📊 開始從 Google Sheets 查詢訂單: ${orderNo}`);

  // 讀取 Google Sheets 資料
  const sheetData = await getSheetData(`${sheetName}!A:AD`);
  
  if (!sheetData || sheetData.length === 0) {
    throw new Error('Google Sheets 沒有資料');
  }

  // 找到對應的訂單行（訂單號碼在 V 欄，索引 21）
  let targetRowIndex = -1;
  for (let i = 0; i < sheetData.length; i++) {
    if (sheetData[i][21] === orderNo) {
      targetRowIndex = i;
      break;
    }
  }

  if (targetRowIndex === -1) {
    throw new Error(`在 Google Sheets 中找不到訂單: ${orderNo}`);
  }

  const orderRow = sheetData[targetRowIndex];
  const paymentStatus = orderRow[22] || ''; // W 欄位：付款狀態

  console.log(`🔍 檢查訂單 ${orderNo} 付款狀態: "${paymentStatus}"`);

  // 檢查是否允許重新付款 - 只要不是已完成付款都可以重新付款
  const disallowedStatuses = ['已完成', '已付款', '付款成功'];
  if (disallowedStatuses.includes(paymentStatus)) {
    throw new Error(`訂單狀態 "${paymentStatus}" 不允許重新付款`);
  }

  console.log(`✅ 訂單 ${orderNo} 狀態 "${paymentStatus}" 允許重新付款`);

  // 解析訂單資料
  const rawEventPrice = orderRow[23];
  const eventPrice = parseInt(rawEventPrice) || 1500; // 預設為 1500 如果解析失敗
  
  const orderData = {
    orderNo: orderRow[21] || '',                    // V: 訂單號碼 (索引21)
    name: orderRow[4] || '',                        // E: 姓名
    email: orderRow[5] || '',                       // F: Email
    eventPrice,                                     // X: 應付金額 (索引23)
    eventName: '錶匠體驗機芯拆解',                  // 活動名稱
    paymentStatus,
    rowIndex: targetRowIndex
  };

  // 驗證必要資料
  if (!orderData.orderNo || !orderData.name || !orderData.email || !orderData.eventPrice) {
    throw new Error('訂單資料不完整，無法重新付款');
  }

  return NextResponse.json({
    success: true,
    orderData
  });
}

/**
 * 更新重新付款狀態
 */
async function handleUpdateRetryPayment(params: { 
  orderNo: string; 
  newOrderNo: string; 
  rowIndex: number 
}) {
  const { orderNo, newOrderNo, rowIndex } = params;
  const sheetName = '工作表1';
  const actualRowNumber = rowIndex + 1; // Google Sheets 行號從 1 開始

  console.log(`📝 記錄重新付款狀態: ${orderNo} → ${newOrderNo}`);

  const sheets = getSheetsClient();
  const sheetId = GOOGLE_SHEETS_CONFIG.getSheetId();

  if (!sheetId) {
    throw new Error('Google Sheet ID 未設定');
  }

  try {
    // 更新付款狀態為「重新付款中」(W 欄位)
    await sheets.updateValues(sheetId, `${sheetName}!W${actualRowNumber}`, [['重新付款中']]);

    // 在 AB 欄位記錄新的訂單號碼（臨時存儲）
    await sheets.updateValues(sheetId, `${sheetName}!AB${actualRowNumber}`, [[newOrderNo]]);

    console.log(`✅ 成功記錄重新付款狀態: ${orderNo} → ${newOrderNo} (第 ${actualRowNumber} 行)`);

    return NextResponse.json({
      success: true,
      message: '重新付款狀態更新成功'
    });

  } catch (sheetsError) {
    console.error(`❌ Google Sheets API 更新失敗:`, sheetsError);
    throw sheetsError;
  }
}
