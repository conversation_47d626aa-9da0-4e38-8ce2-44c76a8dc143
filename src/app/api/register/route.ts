import { NextResponse } from 'next/server';
import { createPaymentRequest, calculateATMExpireDate } from '@/lib/payuni-migration';

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    // 從 body 中取得 eventId, eventPrice, userName, userEmail
    const { eventId, eventPrice, userName, userEmail } = body;

    // 1. 驗證傳入的資料
    if (!eventId || !eventPrice || !userName || !userEmail) {
      return NextResponse.json({ success: false, message: '缺少必要欄位' }, { status: 400 });
    }

    // 計算 ATM 轉帳動態到期日期（基於台灣時間 14:00 判斷）
    const expireDate = calculateATMExpireDate();

    // 2. 建立 PayUni 付款請求所需的資料
    const tradeData = {
      MerchantOrderNo: `pangea_${Date.now()}`,
      Amt: eventPrice || 0,
      ItemDesc: eventId || '',
      Email: userEmail || '',
      LoginType: 0, // 不需登入
      NotifyURL: process.env.PAYUNI_NOTIFY_URL || '', // 支付通知網址
      ReturnURL: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback`, // 付款完成返回網址
      CustomerURL: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback`, // 用戶取消返回網址
      ExpireDate: expireDate, // ATM 轉帳有效期限：當日+2天
    };

    // 3. 產生加密後的付款參數
    const paymentRequest = createPaymentRequest(tradeData);

    // 4. 將付款參數回傳給前端
    return NextResponse.json({ success: true, ...paymentRequest });

  } catch (error) {
    console.error('註冊失敗:', error);
    return NextResponse.json({ success: false, message: '伺服器內部錯誤' }, { status: 500 });
  }
}
