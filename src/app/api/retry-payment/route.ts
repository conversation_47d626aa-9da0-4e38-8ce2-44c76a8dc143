import { NextResponse } from 'next/server';
import { createPaymentRequestEdge, calculateATMExpireDateEdge } from '@/lib/payuni-edge';

// 使用 Edge Runtime + Web Crypto API
export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { orderNo } = body;

    console.log(`🔄 收到重新付款請求，訂單號碼: ${orderNo}`);

    if (!orderNo) {
      console.log('❌ 缺少訂單號碼');
      return NextResponse.json(
        { error: '缺少訂單號碼' },
        { status: 400 }
      );
    }

    // 1. 通過內部 API 查詢訂單資料
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const queryResponse = await fetch(`${baseUrl}/api/internal/google-sheets-operations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'internal-sheets-operation'
      },
      body: JSON.stringify({
        operation: 'queryOrder',
        params: { orderNo }
      })
    });

    if (!queryResponse.ok) {
      const errorData = await queryResponse.json();
      console.log(`❌ 查詢訂單失敗:`, errorData);
      return NextResponse.json(
        { error: errorData.error || '查詢訂單失敗' },
        { status: queryResponse.status }
      );
    }

    const queryResult = await queryResponse.json();
    const orderData = queryResult.orderData;

    console.log(`📊 解析訂單資料:`, orderData);

    // 2. 生成新的訂單號碼
    const newOrderNo = `pangea_${Date.now()}`;
    console.log(`🔄 重新付款生成新訂單號碼: ${orderNo} → ${newOrderNo}`);

    // 3. 通過內部 API 更新重新付款狀態
    const updateResponse = await fetch(`${baseUrl}/api/internal/google-sheets-operations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'internal-sheets-operation'
      },
      body: JSON.stringify({
        operation: 'updateRetryPayment',
        params: { 
          orderNo, 
          newOrderNo, 
          rowIndex: orderData.rowIndex 
        }
      })
    });

    if (!updateResponse.ok) {
      const errorData = await updateResponse.json();
      console.log(`❌ 更新重新付款狀態失敗:`, errorData);
      return NextResponse.json(
        { error: errorData.error || '更新重新付款狀態失敗' },
        { status: updateResponse.status }
      );
    }

    // 4. 建立新的付款請求
    const expireDate = calculateATMExpireDateEdge();

    // 建立 PayUni 付款請求所需的資料
    const paymentRequest = await createPaymentRequestEdge({
      orderNo: newOrderNo,
      eventName: orderData.eventName,
      eventPrice: orderData.eventPrice,
      userName: orderData.name,
      userEmail: orderData.email,
      expireDate: expireDate
    });

    console.log(`✅ 重新付款請求建立成功: ${newOrderNo}`);

    return NextResponse.json({
      success: true,
      message: '重新付款請求建立成功',
      MerID: paymentRequest.MerID,
      EncryptInfo: paymentRequest.EncryptInfo,
      HashInfo: paymentRequest.HashInfo,
      Version: '1.0',
      ApiUrl: paymentRequest.ApiUrl,
      newOrderNo,
      originalOrderNo: orderNo
    });

  } catch (error) {
    console.error('重新付款處理失敗:', error);
    return NextResponse.json(
      { error: '處理失敗，請稍後再試' },
      { status: 500 }
    );
  }
}
