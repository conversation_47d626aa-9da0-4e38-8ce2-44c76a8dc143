import { NextResponse } from 'next/server';
import { getAllFAQs, searchFAQs, getAllTags } from '@/lib/local-faq-edge';

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

/**
 * GET /api/support
 * 獲取常見問題列表（優化版本，使用本地檔案）
 */

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const tag = searchParams.get('tag');
    const search = searchParams.get('search');

    let faqs;
    const allTags = getAllTags();

    // 如果有搜尋關鍵字，搜尋完整的問題資料（包含 answer）
    if (search) {
      faqs = searchFAQs(search);
    }
    // 否則獲取所有問題資料
    else {
      const allFAQs = getAllFAQs();

      // 如果有標籤篩選，進行篩選
      if (tag && tag !== 'all') {
        faqs = allFAQs.filter(faq =>
          faq.tags.some(t => t.toLowerCase().includes(tag.toLowerCase()))
        );
      } else {
        faqs = allFAQs;
      }
    }

    // 標籤已在上面獲取

    const response = NextResponse.json({
      faqs,
      tags: allTags,
      total: faqs.length
    });

    return response;

  } catch (error) {
    console.error('獲取常見問題失敗:', error);
    return NextResponse.json(
      {
        error: '無法獲取常見問題',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
