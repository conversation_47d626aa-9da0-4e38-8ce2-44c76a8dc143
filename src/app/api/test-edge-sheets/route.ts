import { NextRequest, NextResponse } from 'next/server';
import { getSheetData, appendToSheet } from '@/lib/google-sheets-migration';

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

/**
 * 測試 Edge Runtime 相容的 Google Sheets API
 * 用於驗證新的實作是否正常運作
 */
export async function GET() {
  try {
    console.log('🧪 開始測試 Edge Runtime Google Sheets API...');

    // 測試讀取資料
    const testRange = '工作表1!A1:C3';
    console.log(`📖 測試讀取範圍: ${testRange}`);
    
    const data = await getSheetData(testRange);
    console.log('✅ 讀取測試成功:', data);

    return NextResponse.json({
      success: true,
      message: 'Edge Runtime Google Sheets API 測試成功',
      runtime: 'edge',
      testResults: {
        read: {
          success: true,
          range: testRange,
          rowCount: data.length,
          data: data.slice(0, 3) // 只返回前 3 行作為範例
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Edge Runtime Google Sheets API 測試失敗:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Edge Runtime Google Sheets API 測試失敗',
      runtime: 'edge',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * 測試寫入功能
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { testData } = body;

    console.log('🧪 開始測試 Edge Runtime Google Sheets 寫入...');

    // 準備測試資料
    const timestamp = new Date().toISOString();
    const writeData = testData || [
      ['測試時間', '測試類型', '狀態'],
      [timestamp, 'Edge Runtime 測試', '成功']
    ];

    // 測試寫入資料（使用現有的工作表）
    const testRange = '工作表1!A:C';
    console.log(`📝 測試寫入範圍: ${testRange}`);
    
    const result = await appendToSheet(testRange, writeData);
    console.log('✅ 寫入測試成功:', result);

    return NextResponse.json({
      success: true,
      message: 'Edge Runtime Google Sheets 寫入測試成功',
      runtime: 'edge',
      testResults: {
        write: {
          success: true,
          range: testRange,
          rowsWritten: writeData.length,
          result: result
        }
      },
      timestamp
    });

  } catch (error) {
    console.error('❌ Edge Runtime Google Sheets 寫入測試失敗:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Edge Runtime Google Sheets 寫入測試失敗',
      runtime: 'edge',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * 測試認證和權杖管理
 */
export async function PUT() {
  try {
    console.log('🧪 開始測試 Edge Runtime Google Sheets 認證...');

    // 測試多次調用以驗證權杖快取
    const testCalls = [];
    for (let i = 0; i < 3; i++) {
      const startTime = Date.now();
      await getSheetData('工作表1!A1:A1');
      const endTime = Date.now();
      testCalls.push({
        call: i + 1,
        duration: endTime - startTime
      });
    }

    console.log('✅ 認證測試成功');

    return NextResponse.json({
      success: true,
      message: 'Edge Runtime Google Sheets 認證測試成功',
      runtime: 'edge',
      testResults: {
        auth: {
          success: true,
          multipleCalls: testCalls,
          averageDuration: testCalls.reduce((sum, call) => sum + call.duration, 0) / testCalls.length
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Edge Runtime Google Sheets 認證測試失敗:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Edge Runtime Google Sheets 認證測試失敗',
      runtime: 'edge',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
