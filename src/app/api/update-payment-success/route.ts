import { NextRequest, NextResponse } from 'next/server';

/**
 * 更新付款成功狀態到 Google Sheets
 * 用於前景回調時立即更新訂單狀態，不依賴 webhook
 *
 * Edge Runtime 兼容版本 - 通過內部 API 調用來更新 Google Sheets
 */

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

function getPaymentMethodName(paymentType: string): string {
  switch (paymentType) {
    case '1': return '信用卡';
    case '2': return 'ATM轉帳';
    default: return '未知支付方式';
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { orderNo, payuniTradeNo, paymentType, tradeStatus } = body;

    console.log('🔄 收到付款成功更新請求:', {
      orderNo,
      payuniTradeNo,
      paymentType,
      tradeStatus
    });

    // 驗證必要參數
    if (!orderNo) {
      return NextResponse.json(
        { error: '缺少訂單號碼' },
        { status: 400 }
      );
    }

    // 構建模擬的 PayUni webhook 數據
    const webhookData = {
      Status: tradeStatus === '1' ? 'SUCCESS' : 'FAIL',
      Message: tradeStatus === '1' ? '授權成功' : '付款失敗',
      MerTradeNo: orderNo,
      TradeNo: payuniTradeNo || '',
      TradeAmt: '1500', // 預設金額
      TradeStatus: tradeStatus || '1',
      PaymentType: paymentType || '1',
      PaymentDay: new Date().toISOString().replace('T', ' ').replace('Z', '')
    };

    console.log('📝 準備通過 webhook API 更新付款狀態:', webhookData);

    // 調用內部 webhook API 來更新 Google Sheets
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const webhookResponse = await fetch(`${baseUrl}/api/webhook/payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'internal-payment-update'
      },
      body: JSON.stringify({
        // 模擬加密數據，但包含原始數據用於內部處理
        EncryptInfo: 'internal_call',
        HashInfo: 'internal_call',
        _internalCall: true,
        _rawData: webhookData
      })
    });

    if (webhookResponse.ok) {
      const result = await webhookResponse.json();
      console.log('✅ 通過 webhook API 更新成功:', result);

      return NextResponse.json({
        success: true,
        message: '付款狀態更新成功',
        paymentStatus: tradeStatus === '1' ? '已完成' : '付款失敗',
        paymentMethod: getPaymentMethodName(paymentType || '1')
      });
    } else {
      console.error('❌ webhook API 調用失敗:', webhookResponse.status);
      throw new Error('內部 API 調用失敗');
    }

  } catch (error) {
    console.error('❌ 更新付款狀態失敗:', error);
    return NextResponse.json(
      { error: '更新付款狀態失敗' },
      { status: 500 }
    );
  }
}


