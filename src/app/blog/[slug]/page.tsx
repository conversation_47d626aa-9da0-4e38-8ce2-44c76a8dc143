import { Metadata } from 'next';
import { Suspense } from 'react';
import { getMdxPostBySlug } from '@/lib/mdx-utils';
import { generateDynamicMetadata } from '@/lib/seo-utils';
import { generateBlogStaticParams } from '@/lib/static-paths';
import MdxBlogDetailContent from '@/components/MdxBlogDetailContent';
import BlogDetailSkeleton from '@/components/BlogDetailSkeleton';

// 生成靜態路徑
export function generateStaticParams() {
  return generateBlogStaticParams();
}

// 生成 metadata
export async function generateMetadata({
  params
}: {
  params: Promise<{ slug: string }>
}): Promise<Metadata> {
  const resolvedParams = await params;
  const post = getMdxPostBySlug(resolvedParams.slug);

  if (!post) {
    return {
      title: '文章不存在',
      description: '找不到指定的文章',
    };
  }

  // 使用簡化的動態 metadata 生成
  const title = post.frontmatter.seoTitle || `${post.frontmatter.title} - <PERSON>aven Blog`;
  const description = post.frontmatter.seoDescription || post.frontmatter.title;
  const image = post.frontmatter.socialImage || post.frontmatter.thumbnail;

  return generateDynamicMetadata(title, description, image, `/blog/${post.slug}`);
}

const BlogPostPage = async ({ params }: { params: Promise<{ slug: string }> }) => {
  const resolvedParams = await params;

  return (
    <Suspense fallback={<BlogDetailSkeleton />}>
      <MdxBlogDetailContent slug={resolvedParams.slug} />
    </Suspense>
  );
};

export default BlogPostPage;
