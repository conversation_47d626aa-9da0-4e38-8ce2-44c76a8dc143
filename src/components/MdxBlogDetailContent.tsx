import Link from 'next/link';
import Image from 'next/image';
import { getMdxPostBySlug } from '@/lib/mdx-utils';
import { notFound } from 'next/navigation';
import { MDXRemote } from 'next-mdx-remote/rsc';
import mdxComponents from '@/components/MdxComponents';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';

interface MdxBlogDetailContentProps {
  slug: string;
}

// 格式化日期顯示
function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(date);
}

export default function MdxBlogDetailContent({ slug }: MdxBlogDetailContentProps) {
  const post = getMdxPostBySlug(slug);

  if (!post) {
    notFound();
  }

  const publishDate = new Date(post.frontmatter.publishDate);

  return (
    <div className="min-h-screen bg-white">
      {/* 返回按鈕 */}
      <div className="border-b border-slate-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            href="/blog"
            className="inline-flex items-center text-slate-600 hover:text-slate-800 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            返回
          </Link>
        </div>
      </div>

      {/* 文章內容 */}
      <article className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 文章標題 */}
          <header className="mb-8">
            <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6 leading-tight">
              {post.frontmatter.title}
            </h1>

            {/* 文章資訊 */}
            <div className="flex items-center text-slate-600 mb-8 space-x-4">
              <time dateTime={publishDate.toISOString()}>
                {formatDate(publishDate)}
              </time>
              <span>•</span>
              <span>{post.readingTime} 分鐘閱讀</span>
              {post.frontmatter.tags && post.frontmatter.tags.length > 0 && (
                <>
                  <span>•</span>
                  <div className="flex flex-wrap gap-2">
                    {post.frontmatter.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 text-xs rounded-full bg-slate-100 text-slate-700"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </>
              )}
            </div>
          </header>

          {/* Hero 圖片區域 */}
          {post.frontmatter.thumbnail && (
            <div className="mb-12">
              <div className="relative w-full h-96">
                <Image
                  src={post.frontmatter.thumbnail}
                  alt={post.frontmatter.title}
                  fill
                  className="rounded-xl object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                  priority
                  quality={90}
                />
              </div>
            </div>
          )}

          {/* MDX 內容 */}
          <div className="prose prose-lg max-w-none mb-8 blog-content">
            <MDXRemote
              source={post.content}
              components={mdxComponents}
              options={{
                mdxOptions: {
                  remarkPlugins: [remarkGfm],
                  rehypePlugins: [rehypeHighlight],
                },
              }}
            />
          </div>

          {/* 作者介紹區塊 */}
          <div className="pt-4 border-t border-slate-200">
            <div className="prose prose-lg max-w-none">
              <h2 className="text-2xl font-bold mb-4" style={{ color: '#2b354d' }}>作者介紹</h2>
              <p className="mb-4 leading-relaxed" style={{ color: '#2b354d' }}>
                Weaven 是一個專注於機械錶相關產品服務的台灣團隊，致力於「讓享受機械錶變簡單」，產品包括：Pangea 機械錶智慧收藏盒、AQUA PURE 瑠璃冰河清潔組，亦不定期舉辦錶匠體驗活動，讓大家有機會體驗機械錶精細與奧妙。
              </p>
              <p style={{ color: '#2b354d' }}>
                這裡可以找到我們：
                <a
                  href="https://www.facebook.com/weaven.co/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 transition-colors mx-1"
                >
                  Facebook
                </a>
                |
                <a
                  href="https://www.instagram.com/weaven2019/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 transition-colors mx-1"
                >
                  Instagram
                </a>
              </p>
            </div>
          </div>


        </div>
      </article>
    </div>
  );
}
