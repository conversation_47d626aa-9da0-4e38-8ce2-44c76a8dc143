/**
 * Edge Runtime 相容的部落格資料存取層
 * 使用預處理的 JSON 資料，完全相容 Cloudflare Pages Edge Runtime
 */

// 型別定義
export interface BlogFrontmatter {
  title: string;
  publishDate: string;
  author: string;
  thumbnail: string;
  seoTitle: string;
  seoDescription: string;
  socialImage: string;
  socialTitle: string;
  socialDescription: string;
  slug: string;
  tags: string[];
  excerpt: string;
  featured: boolean;
}

export interface ProcessedPost {
  slug: string;
  frontmatter: BlogFrontmatter;
  content: string;
  readingTime: number;
  filename: string;
}

export interface BlogListItem {
  frontmatter: BlogFrontmatter;
  slug: string;
  excerpt: string;
  readingTime: number;
}

export interface BlogData {
  posts: ProcessedPost[];
  metadata: {
    totalPosts: number;
    lastUpdated: string;
    tags: string[];
    processedAt: string;
  };
  index: {
    bySlug: Record<string, number>;
    byTag: Record<string, number[]>;
    byDate: Record<string, number[]>;
  };
}

/**
 * 載入部落格資料
 * 使用動態 import 以支援 Edge Runtime
 */
async function loadBlogData(): Promise<BlogData> {
  try {
    // 動態 import JSON 資料
    const blogData = await import('@/data/blog.json');
    return blogData.default as BlogData;
  } catch (error) {
    console.error('載入部落格資料失敗:', error);
    // 返回空資料結構
    return {
      posts: [],
      metadata: {
        totalPosts: 0,
        lastUpdated: new Date().toISOString(),
        tags: [],
        processedAt: new Date().toISOString(),
      },
      index: { bySlug: {}, byTag: {}, byDate: {} },
    };
  }
}

/**
 * 獲取所有文章列表（Edge Runtime 版本）
 */
export async function getAllPostsEdge(): Promise<BlogListItem[]> {
  const blogData = await loadBlogData();
  
  return blogData.posts.map(post => ({
    frontmatter: post.frontmatter,
    slug: post.slug,
    excerpt: post.frontmatter.excerpt,
    readingTime: post.readingTime,
  }));
}

/**
 * 根據 slug 獲取特定文章（Edge Runtime 版本）
 */
export async function getPostBySlugEdge(slug: string): Promise<ProcessedPost | null> {
  const blogData = await loadBlogData();
  
  // 使用索引快速查找
  const index = blogData.index.bySlug[slug];
  if (index !== undefined && blogData.posts[index]) {
    return blogData.posts[index];
  }
  
  return null;
}

/**
 * 獲取分頁文章列表（Edge Runtime 版本）
 */
export async function getPaginatedPostsEdge(page: number = 0, pageSize: number = 12): Promise<{
  posts: BlogListItem[];
  total: number;
  hasMore: boolean;
  page: number;
  pageSize: number;
}> {
  const allPosts = await getAllPostsEdge();
  const startIndex = page * pageSize;
  const endIndex = startIndex + pageSize;
  
  const posts = allPosts.slice(startIndex, endIndex);
  const hasMore = endIndex < allPosts.length;
  
  return {
    posts,
    total: allPosts.length,
    hasMore,
    page,
    pageSize,
  };
}

/**
 * 獲取精選文章（Edge Runtime 版本）
 */
export async function getFeaturedPostsEdge(limit: number = 3): Promise<BlogListItem[]> {
  const allPosts = await getAllPostsEdge();
  return allPosts
    .filter(post => post.frontmatter.featured)
    .slice(0, limit);
}

/**
 * 根據標籤獲取文章（Edge Runtime 版本）
 */
export async function getPostsByTagEdge(tag: string): Promise<BlogListItem[]> {
  const blogData = await loadBlogData();
  
  // 使用索引快速查找
  const postIndexes = blogData.index.byTag[tag] || [];
  
  return postIndexes.map(index => {
    const post = blogData.posts[index];
    return {
      frontmatter: post.frontmatter,
      slug: post.slug,
      excerpt: post.frontmatter.excerpt,
      readingTime: post.readingTime,
    };
  });
}

/**
 * 根據標籤獲取相關文章（Edge Runtime 版本）
 */
export async function getRelatedPostsEdge(
  currentSlug: string, 
  tags: string[], 
  limit: number = 3
): Promise<BlogListItem[]> {
  const allPosts = await getAllPostsEdge();
  
  return allPosts
    .filter(post => post.slug !== currentSlug) // 排除當前文章
    .filter(post => {
      // 檢查是否有共同標籤
      const postTags = post.frontmatter.tags || [];
      return tags.some(tag => postTags.includes(tag));
    })
    .slice(0, limit);
}

/**
 * 搜尋文章（Edge Runtime 版本）
 */
export async function searchPostsEdge(query: string): Promise<BlogListItem[]> {
  const allPosts = await getAllPostsEdge();
  const lowercaseQuery = query.toLowerCase();
  
  return allPosts.filter(post => {
    const { title, excerpt, tags } = post.frontmatter;

    return (
      title.toLowerCase().includes(lowercaseQuery) ||
      (excerpt || '').toLowerCase().includes(lowercaseQuery) ||
      (tags || []).some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  });
}

/**
 * 獲取所有可用的標籤（Edge Runtime 版本）
 */
export async function getAllTagsEdge(): Promise<string[]> {
  const blogData = await loadBlogData();
  return blogData.metadata.tags;
}

/**
 * 獲取部落格統計資訊（Edge Runtime 版本）
 */
export async function getBlogStatsEdge(): Promise<{
  totalPosts: number;
  totalTags: number;
  lastUpdated: string;
  featuredPosts: number;
}> {
  const blogData = await loadBlogData();
  const featuredCount = blogData.posts.filter(post => post.frontmatter.featured).length;
  
  return {
    totalPosts: blogData.metadata.totalPosts,
    totalTags: blogData.metadata.tags.length,
    lastUpdated: blogData.metadata.lastUpdated,
    featuredPosts: featuredCount,
  };
}

/**
 * 根據日期範圍獲取文章（Edge Runtime 版本）
 */
export async function getPostsByDateRangeEdge(
  startDate: string, 
  endDate: string
): Promise<BlogListItem[]> {
  const allPosts = await getAllPostsEdge();
  
  return allPosts.filter(post => {
    const publishDate = post.frontmatter.publishDate;
    return publishDate >= startDate && publishDate <= endDate;
  });
}

/**
 * 獲取最新文章（Edge Runtime 版本）
 */
export async function getLatestPostsEdge(limit: number = 5): Promise<BlogListItem[]> {
  const allPosts = await getAllPostsEdge();
  return allPosts.slice(0, limit);
}

/**
 * 驗證部落格資料完整性（Edge Runtime 版本）
 */
export async function validateBlogDataEdge(): Promise<{
  isValid: boolean;
  errors: string[];
  warnings: string[];
}> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    const blogData = await loadBlogData();

    // 檢查基本結構
    if (!blogData.posts || !Array.isArray(blogData.posts)) {
      errors.push('部落格資料結構無效');
      return { isValid: false, errors, warnings };
    }

    // 檢查每篇文章
    blogData.posts.forEach((post, index) => {
      if (!post.slug) {
        errors.push(`文章 ${index} 缺少 slug`);
      }

      if (!post.frontmatter.title) {
        errors.push(`文章 ${post.slug || index} 缺少標題`);
      }

      if (!post.frontmatter.publishDate) {
        warnings.push(`文章 ${post.slug || index} 缺少發布日期`);
      }

      if (!post.frontmatter.seoDescription) {
        warnings.push(`文章 ${post.slug || index} 缺少 SEO 描述`);
      }
    });

    // 檢查 slug 重複
    const slugs = blogData.posts.map(post => post.slug);
    const duplicateSlugs = slugs.filter((slug, index) => slugs.indexOf(slug) !== index);
    if (duplicateSlugs.length > 0) {
      errors.push(`發現重複的 slug: ${duplicateSlugs.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };

  } catch (error) {
    errors.push(`驗證過程中發生錯誤: ${error instanceof Error ? error.message : String(error)}`);
    return { isValid: false, errors, warnings };
  }
}

// ===== 相容性層：保持與原有 mdx-utils.ts 相同的介面 =====

/**
 * 相容原有的 getAllMdxPosts 函數
 */
export async function getAllMdxPosts(): Promise<BlogListItem[]> {
  return await getAllPostsEdge();
}

/**
 * 相容原有的 getMdxPostBySlug 函數
 */
export async function getMdxPostBySlug(slug: string): Promise<ProcessedPost | null> {
  return await getPostBySlugEdge(slug);
}

/**
 * 相容原有的 getPaginatedMdxPosts 函數
 */
export async function getPaginatedMdxPosts(page: number = 0, pageSize: number = 12) {
  return await getPaginatedPostsEdge(page, pageSize);
}

/**
 * 相容原有的 getFeaturedMdxPosts 函數
 */
export async function getFeaturedMdxPosts(limit: number = 3): Promise<BlogListItem[]> {
  return await getFeaturedPostsEdge(limit);
}

/**
 * 相容原有的 getRelatedMdxPosts 函數
 */
export async function getRelatedMdxPosts(
  currentSlug: string,
  tags: string[],
  limit: number = 3
): Promise<BlogListItem[]> {
  return await getRelatedPostsEdge(currentSlug, tags, limit);
}

/**
 * 相容原有的 getAllTags 函數
 */
export async function getAllTags(): Promise<string[]> {
  return await getAllTagsEdge();
}

/**
 * 相容原有的 getPostsByTag 函數
 */
export async function getPostsByTag(tag: string): Promise<BlogListItem[]> {
  return await getPostsByTagEdge(tag);
}

/**
 * 相容原有的 searchMdxPosts 函數
 */
export async function searchMdxPosts(query: string): Promise<BlogListItem[]> {
  return await searchPostsEdge(query);
}
