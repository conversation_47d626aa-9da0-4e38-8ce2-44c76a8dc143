/**
 * Google Sheets API 快取實作
 * 解決 API 回應時間過慢的問題
 */

import { cachePerformanceMonitor } from './cache-performance-monitor';

interface CacheItem<T = unknown> {
  data: T;
  timestamp: number;
  key: string;
}

interface CacheConfig {
  ttl: number;           // 快取存活時間 (毫秒)
  maxSize: number;       // 最大快取項目數
  enableLogging: boolean; // 是否啟用日誌
}

class MemoryCache {
  private cache = new Map<string, CacheItem>();
  public readonly config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      ttl: 5 * 60 * 1000,        // 預設 5 分鐘
      maxSize: 100,              // 預設最多 100 個項目
      enableLogging: true,       // 預設啟用日誌
      ...config
    };
  }

  /**
   * 獲取快取資料，如果不存在或過期則執行 fetchFunction
   */
  async get<T>(key: string, fetchFunction: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    
    // 檢查快取
    const cached = this.cache.get(key);
    const now = Date.now();
    
    if (cached && (now - cached.timestamp) < this.config.ttl) {
      const duration = performance.now() - startTime;
      cachePerformanceMonitor.recordCacheHit(key, duration);
      return cached.data as T;
    }

    // 快取未命中或已過期，重新獲取資料
    if (this.config.enableLogging) {
      console.log(`📡 快取未命中，重新獲取: ${key}`);
    }

    try {
      const data = await fetchFunction();
      const duration = performance.now() - startTime;

      // 儲存到快取
      this.set(key, data);

      // 記錄效能指標
      const dataSize = JSON.stringify(data).length;
      cachePerformanceMonitor.recordCacheMiss(key, duration, dataSize);
      cachePerformanceMonitor.recordCacheSet(key, dataSize);

      return data;
    } catch (error) {
      const duration = performance.now() - startTime;
      cachePerformanceMonitor.recordCacheMiss(key, duration);

      if (this.config.enableLogging) {
        console.error(`❌ 獲取資料失敗: ${key}`, error);
      }
      throw error;
    }
  }

  /**
   * 設定快取項目
   */
  set<T>(key: string, data: T): void {
    // 檢查快取大小限制
    if (this.cache.size >= this.config.maxSize) {
      this.evictOldest();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      key
    });
  }

  /**
   * 刪除最舊的快取項目
   */
  private evictOldest(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      if (this.config.enableLogging) {
        console.log(`🗑️ 清除最舊快取: ${oldestKey}`);
      }
    }
  }

  /**
   * 清除指定快取
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted && this.config.enableLogging) {
      console.log(`🗑️ 清除快取: ${key}`);
    }
    return deleted;
  }

  /**
   * 清除所有快取
   */
  clear(): void {
    this.cache.clear();
    if (this.config.enableLogging) {
      console.log('🗑️ 清除所有快取');
    }
  }

  /**
   * 清除過期快取
   */
  cleanExpired(): number {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, item] of this.cache.entries()) {
      if ((now - item.timestamp) >= this.config.ttl) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0 && this.config.enableLogging) {
      console.log(`🧹 清除 ${cleanedCount} 個過期快取`);
    }

    return cleanedCount;
  }

  /**
   * 獲取快取統計資訊
   */
  getStats() {
    const now = Date.now();
    let expiredCount = 0;
    let totalSize = 0;

    for (const item of this.cache.values()) {
      if ((now - item.timestamp) >= this.config.ttl) {
        expiredCount++;
      }
      totalSize += JSON.stringify(item.data).length;
    }

    return {
      totalItems: this.cache.size,
      expiredItems: expiredCount,
      activeItems: this.cache.size - expiredCount,
      totalSizeBytes: totalSize,
      averageSizeBytes: this.cache.size > 0 ? totalSize / this.cache.size : 0,
      config: this.config
    };
  }
}

// 快取配置（支援環境變數控制）
const CACHE_CONFIG = {
  ttl: parseInt(process.env.CACHE_TTL || '300000'),           // 預設 5 分鐘
  maxSize: parseInt(process.env.CACHE_MAX_SIZE || '50'),      // 預設 50 個項目
  enableLogging: process.env.CACHE_LOGGING === 'true' || process.env.NODE_ENV === 'development',
  enabled: process.env.CACHE_ENABLED !== 'false'             // 預設啟用
};

// 建立全域快取實例
const sheetsCache = new MemoryCache(CACHE_CONFIG);

// 定期清理過期快取
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    sheetsCache.cleanExpired();
  }, 60 * 1000); // 每分鐘清理一次
}

// 為不同 TTL 建立專用快取實例
const blogCache = new MemoryCache({
  ttl: 5 * 60 * 1000,        // 5 分鐘
  maxSize: CACHE_CONFIG.maxSize,
  enableLogging: CACHE_CONFIG.enableLogging
});

const watchCache = new MemoryCache({
  ttl: 5 * 60 * 1000,       // 5 分鐘
  maxSize: CACHE_CONFIG.maxSize,
  enableLogging: CACHE_CONFIG.enableLogging
});

const faqCache = new MemoryCache({
  ttl: 10 * 60 * 1000,       // 10 分鐘
  maxSize: CACHE_CONFIG.maxSize,
  enableLogging: CACHE_CONFIG.enableLogging
});

/**
 * 快取包裝函數 - 用於包裝 Google Sheets API 呼叫
 */
export async function withCache<T>(
  key: string,
  fetchFunction: () => Promise<T>
): Promise<T> {
  // 如果快取被停用，直接執行函數
  if (!CACHE_CONFIG.enabled) {
    return fetchFunction();
  }

  // 使用全域快取實例
  return sheetsCache.get(key, fetchFunction);
}

/**
 * 快取的 Google Sheets 資料讀取函數
 * 注意：部落格相關函數已移除，現在使用 MDX 檔案系統
 */

export async function getCachedWatchListData() {
  const { getWatchListSheetData } = await import('@/lib/google-sheets-migration');

  return watchCache.get('watch-list-data', () => getWatchListSheetData());
}

// 部落格相關快取函數已移除，現在使用 MDX 檔案系統

export async function getCachedWatchDetailData(range: string) {
  const { getWatchSheetData } = await import('@/lib/google-sheets-migration');

  return withCache(
    `watch-detail-${range}`,
    () => getWatchSheetData(range)
  );
}

/**
 * 手動清除特定快取
 */
export function clearCache(pattern?: string) {
  if (!pattern) {
    sheetsCache.clear();
    watchCache.clear();
    faqCache.clear();
    console.log('🧹 已清除所有快取');
    return;
  }

  // 清除符合模式的快取
  console.log(`🧹 清除符合模式 "${pattern}" 的快取`);

  // 根據模式清除特定類型的快取
  switch (pattern.toLowerCase()) {
    case 'blog':
      blogCache.clear();
      sheetsCache.delete('blog-list-data');
      console.log('✅ 已清除部落格相關快取');
      break;

    case 'watch':
      watchCache.clear();
      sheetsCache.delete('watch-list-data');
      console.log('✅ 已清除手錶相關快取');
      break;

    case 'faq':
      faqCache.clear();
      sheetsCache.delete('faq-all-data');
      console.log('✅ 已清除 FAQ 相關快取');
      break;

    default:
      // 嘗試直接清除指定的 key
      sheetsCache.delete(pattern);
      blogCache.delete(pattern);
      watchCache.delete(pattern);
      faqCache.delete(pattern);
      console.log(`✅ 已清除快取 key: ${pattern}`);
      break;
  }
}

/**
 * 獲取快取統計資訊
 */
export function getCacheStats() {
  const mainStats = sheetsCache.getStats();
  const blogStats = blogCache.getStats();
  const watchStats = watchCache.getStats();
  const faqStats = faqCache.getStats();

  return {
    main: mainStats,
    blog: blogStats,
    watch: watchStats,
    faq: faqStats,
    combined: {
      totalItems: mainStats.totalItems + blogStats.totalItems + watchStats.totalItems + faqStats.totalItems,
      activeItems: mainStats.activeItems + blogStats.activeItems + watchStats.activeItems + faqStats.activeItems,
      expiredItems: mainStats.expiredItems + blogStats.expiredItems + watchStats.expiredItems + faqStats.expiredItems,
      totalSizeBytes: mainStats.totalSizeBytes + blogStats.totalSizeBytes + watchStats.totalSizeBytes + faqStats.totalSizeBytes
    }
  };
}

/**
 * 快取的 FAQ 資料讀取函數
 */
export async function getCachedFAQData() {
  const { getAllFAQs } = await import('@/lib/local-faq-edge');

  return faqCache.get('faq-all-data', () => Promise.resolve(getAllFAQs()));
}

/**
 * 預熱快取 - 在應用啟動時預先載入常用資料
 */
export async function warmupCache() {
  console.log('🔥 開始預熱快取...');

  try {
    // 部落格現在使用 MDX 檔案系統，不需要快取預熱
    console.log('✅ 部落格使用 MDX 檔案系統，無需預熱');

    // 預載入手錶列表
    await getCachedWatchListData();
    console.log('✅ 手錶列表快取已預熱');

    // 預載入 FAQ 資料
    await getCachedFAQData();
    console.log('✅ FAQ 資料快取已預熱');

    console.log('🎉 快取預熱完成');
  } catch (error) {
    console.error('❌ 快取預熱失敗:', error);
  }
}

// 定義全域快取管理器類型
interface GlobalCacheManager {
  getStats: typeof getCacheStats;
  clearCache: typeof clearCache;
  warmupCache: typeof warmupCache;
  cache: MemoryCache;
}

// 擴展全域物件類型
declare global {
  var __cacheManager: GlobalCacheManager | undefined;
}

// 在開發環境中提供快取管理 API
if (process.env.NODE_ENV === 'development') {
  // 將快取管理函數掛載到全域物件，方便除錯
  if (typeof globalThis !== 'undefined') {
    globalThis.__cacheManager = {
      getStats: getCacheStats,
      clearCache,
      warmupCache,
      cache: sheetsCache
    };

    console.log('🛠️ 快取管理工具已掛載到 globalThis.__cacheManager');
  }
}

export default sheetsCache;
