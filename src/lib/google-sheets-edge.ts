/**
 * Google Sheets API Edge Runtime 相容客戶端
 * 使用 Web Standards API，完全相容 Cloudflare Pages Edge Runtime
 */

import { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';

// Google Sheets API 基礎 URL
const SHEETS_API_BASE = 'https://sheets.googleapis.com/v4/spreadsheets';

// JWT 相關常數
const JWT_HEADER = {
  alg: 'RS256',
  typ: 'JWT'
};

const GOOGLE_TOKEN_URL = 'https://oauth2.googleapis.com/token';
const GOOGLE_SCOPE = 'https://www.googleapis.com/auth/spreadsheets';

/**
 * Edge Runtime 相容的 Google Sheets 客戶端
 */
export class EdgeSheetsClient {
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  /**
   * 取得有效的存取權杖
   */
  private async getAccessToken(): Promise<string> {
    // 檢查現有權杖是否仍然有效（提前 5 分鐘更新）
    const now = Math.floor(Date.now() / 1000);
    if (this.accessToken && this.tokenExpiry > now + 300) {
      return this.accessToken;
    }

    // 產生新的 JWT 權杖
    const jwt = await this.createJWT();
    
    // 使用 JWT 換取存取權杖
    const response = await fetch(GOOGLE_TOKEN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        assertion: jwt,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`取得存取權杖失敗: ${response.status} ${error}`);
    }

    const tokenData = await response.json();
    this.accessToken = tokenData.access_token;
    this.tokenExpiry = now + tokenData.expires_in;

    if (!this.accessToken) {
      throw new Error('無法取得 Google Sheets 存取權杖');
    }

    return this.accessToken;
  }

  /**
   * 使用 Web Crypto API 建立 JWT
   */
  private async createJWT(): Promise<string> {
    const serviceAccountEmail = GOOGLE_SHEETS_CONFIG.getServiceAccountEmail();
    const privateKeyPem = GOOGLE_SHEETS_CONFIG.getPrivateKey();

    if (!serviceAccountEmail || !privateKeyPem) {
      throw new Error('缺少 Google Service Account 憑證');
    }

    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: serviceAccountEmail,
      scope: GOOGLE_SCOPE,
      aud: GOOGLE_TOKEN_URL,
      exp: now + 3600, // 1 小時後過期
      iat: now,
    };

    // 編碼 Header 和 Payload
    const encodedHeader = this.base64UrlEncode(JSON.stringify(JWT_HEADER));
    const encodedPayload = this.base64UrlEncode(JSON.stringify(payload));
    const unsignedToken = `${encodedHeader}.${encodedPayload}`;

    // 使用 Web Crypto API 簽名
    const signature = await this.signJWT(unsignedToken, privateKeyPem);
    const encodedSignature = this.base64UrlEncode(signature);

    return `${unsignedToken}.${encodedSignature}`;
  }

  /**
   * 使用 Web Crypto API 簽名 JWT
   */
  private async signJWT(data: string, privateKeyPem: string): Promise<ArrayBuffer> {
    // 清理 PEM 格式的私鑰
    const pemHeader = '-----BEGIN PRIVATE KEY-----';
    const pemFooter = '-----END PRIVATE KEY-----';
    const pemContents = privateKeyPem
      .replace(pemHeader, '')
      .replace(pemFooter, '')
      .replace(/\s/g, '');

    // 解碼 Base64 私鑰
    const binaryDer = Uint8Array.from(atob(pemContents), c => c.charCodeAt(0));

    // 匯入私鑰
    const cryptoKey = await crypto.subtle.importKey(
      'pkcs8',
      binaryDer,
      {
        name: 'RSASSA-PKCS1-v1_5',
        hash: 'SHA-256',
      },
      false,
      ['sign']
    );

    // 簽名資料
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    
    return await crypto.subtle.sign('RSASSA-PKCS1-v1_5', cryptoKey, dataBuffer);
  }

  /**
   * Base64 URL 編碼（無填充）
   */
  private base64UrlEncode(data: string | ArrayBuffer): string {
    let base64: string;
    
    if (typeof data === 'string') {
      base64 = btoa(data);
    } else {
      const bytes = new Uint8Array(data);
      const binary = Array.from(bytes, byte => String.fromCharCode(byte)).join('');
      base64 = btoa(binary);
    }
    
    return base64
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * 從 Google Sheets 讀取資料
   */
  async getValues(spreadsheetId: string, range: string): Promise<string[][]> {
    const accessToken = await this.getAccessToken();
    
    const url = `${SHEETS_API_BASE}/${spreadsheetId}/values/${encodeURIComponent(range)}`;
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`讀取 Google Sheets 失敗: ${response.status} ${error}`);
    }

    const data = await response.json();
    return data.values || [];
  }

  /**
   * 向 Google Sheets 附加資料
   */
  async appendValues(
    spreadsheetId: string,
    range: string,
    values: (string | number | boolean | null)[][]
  ): Promise<void> {
    const accessToken = await this.getAccessToken();

    const url = `${SHEETS_API_BASE}/${spreadsheetId}/values/${encodeURIComponent(range)}:append?valueInputOption=USER_ENTERED`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        values,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`寫入 Google Sheets 失敗: ${response.status} ${error}`);
    }
  }

  /**
   * 更新 Google Sheets 特定範圍的資料
   */
  async updateValues(
    spreadsheetId: string,
    range: string,
    values: (string | number | boolean | null)[][]
  ): Promise<void> {
    const accessToken = await this.getAccessToken();

    const url = `${SHEETS_API_BASE}/${spreadsheetId}/values/${encodeURIComponent(range)}?valueInputOption=USER_ENTERED`;

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        values,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`更新 Google Sheets 失敗: ${response.status} ${error}`);
    }
  }
}

// 全域客戶端實例（單例模式）
let edgeSheetsClient: EdgeSheetsClient | null = null;

/**
 * 取得 Edge Runtime 相容的 Google Sheets 客戶端
 */
export function getEdgeSheetsClient(): EdgeSheetsClient {
  if (!edgeSheetsClient) {
    edgeSheetsClient = new EdgeSheetsClient();
  }
  return edgeSheetsClient;
}

/**
 * Edge Runtime 相容的 Google Sheets 操作函數
 */

/**
 * 從指定的 Google Sheet 讀取資料（Edge Runtime 版本）
 */
export async function getSheetDataEdge(range: string): Promise<string[][]> {
  const sheetId = GOOGLE_SHEETS_CONFIG.getSheetId();
  if (!sheetId) {
    throw new Error('Google Sheet ID 未設定');
  }

  const client = getEdgeSheetsClient();
  return await client.getValues(sheetId, range);
}

/**
 * 將資料附加到指定的 Google Sheet 中（Edge Runtime 版本）
 */
export async function appendToSheetEdge(
  range: string, 
  values: (string | number | boolean | null)[][]
): Promise<void> {
  const sheetId = GOOGLE_SHEETS_CONFIG.getSheetId();
  if (!sheetId) {
    throw new Error('Google Sheet ID 未設定');
  }

  const client = getEdgeSheetsClient();
  await client.appendValues(sheetId, range, values);
}

/**
 * 從手錶專用的 Google Sheet 讀取資料（Edge Runtime 版本）
 */
export async function getWatchSheetDataEdge(range: string): Promise<string[][]> {
  const sheetId = GOOGLE_SHEETS_CONFIG.getWatchSheetId();
  if (!sheetId) {
    throw new Error('手錶 Google Sheet ID 未設定');
  }

  const client = getEdgeSheetsClient();
  return await client.getValues(sheetId, range);
}

/**
 * 從部落格專用的 Google Sheet 讀取資料（Edge Runtime 版本）
 */
export async function getBlogSheetDataEdge(range: string): Promise<string[][]> {
  const sheetId = GOOGLE_SHEETS_CONFIG.getBlogSheetId();
  if (!sheetId) {
    throw new Error('部落格 Google Sheet ID 未設定');
  }

  const client = getEdgeSheetsClient();
  return await client.getValues(sheetId, range);
}
