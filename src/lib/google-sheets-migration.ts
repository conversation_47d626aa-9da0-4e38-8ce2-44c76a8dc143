/**
 * Google Sheets API 遷移工具
 * 提供從 Node.js googleapis 到 Edge Runtime 的遷移函數
 */

import { 
  getEdgeSheetsClient,
  getSheetDataEdge,
  appendToSheetEdge,
  getWatchSheetDataEdge,
  getBlogSheetDataEdge
} from './google-sheets-edge';
import { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';

/**
 * 遷移後的 Google Sheets 工具函數
 * 保持與原有 API 相同的介面，但使用 Edge Runtime 相容實作
 */

/**
 * 取得經過授權的 Google Sheets API 客戶端（Edge Runtime 版本）
 * 替代原有的 getSheetsClient() 函數
 */
export function getSheetsClientEdge() {
  return getEdgeSheetsClient();
}

/**
 * 將資料附加到指定的 Google Sheet 中（Edge Runtime 版本）
 * 替代原有的 appendToSheet() 函數
 */
export async function appendToSheet(
  range: string, 
  values: (string | number | boolean | null)[][]
): Promise<unknown> {
  try {
    await appendToSheetEdge(range, values);
    
    // 返回與原有 API 相容的回應格式
    return {
      spreadsheetId: GOOGLE_SHEETS_CONFIG.getSheetId(),
      tableRange: range,
      updates: {
        spreadsheetId: GOOGLE_SHEETS_CONFIG.getSheetId(),
        updatedRange: range,
        updatedRows: values.length,
        updatedColumns: values[0]?.length || 0,
        updatedCells: values.length * (values[0]?.length || 0)
      }
    };
  } catch (error) {
    console.error('寫入 Google Sheet 失敗:', error);
    throw new Error('無法寫入 Google Sheet');
  }
}

/**
 * 從指定的 Google Sheet 讀取資料（Edge Runtime 版本）
 * 替代原有的 getSheetData() 函數
 */
export async function getSheetData(range: string): Promise<string[][]> {
  try {
    console.log(`📡 從 Google Sheets 讀取資料: ${range}`);
    const data = await getSheetDataEdge(range);
    return data;
  } catch (error) {
    console.error('讀取 Google Sheet 失敗:', error);
    throw new Error('無法讀取 Google Sheet');
  }
}

/**
 * 從手錶專用的 Google Sheet 讀取資料（Edge Runtime 版本）
 * 替代原有的 getWatchSheetData() 函數
 */
export async function getWatchSheetData(range: string): Promise<string[][]> {
  try {
    console.log(`📡 從手錶 Google Sheets 讀取資料: ${range}`);
    const data = await getWatchSheetDataEdge(range);
    return data;
  } catch (error) {
    console.error('讀取手錶 Google Sheet 失敗:', error);
    throw new Error('無法讀取手錶 Google Sheet');
  }
}

/**
 * 從部落格專用的 Google Sheet 讀取資料（Edge Runtime 版本）
 * 替代原有的 getBlogSheetData() 函數
 */
export async function getBlogSheetData(range: string): Promise<string[][]> {
  try {
    console.log(`📡 從部落格 Google Sheets 讀取資料: ${range}`);
    const data = await getBlogSheetDataEdge(range);
    return data;
  } catch (error) {
    console.error('讀取部落格 Google Sheet 失敗:', error);
    
    if (error instanceof Error) {
      // 提供更具體的錯誤訊息
      if (error.message.includes('Unable to parse range')) {
        throw new Error(`Google Sheets 範圍格式錯誤: ${range}。請確認工作表名稱 "Blog" 是否存在。`);
      } else if (error.message.includes('not found')) {
        throw new Error(`找不到 Google Sheets 或工作表。請檢查 Sheet ID 和工作表名稱。`);
      } else if (error.message.includes('permission')) {
        throw new Error('Google Sheets 權限不足。請確認服務帳戶有存取權限。');
      }
    }

    throw new Error(`無法讀取部落格 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 從部落格專用的 Google Sheet 讀取文章列表資料（優化版本，Edge Runtime）
 * 替代原有的 getBlogListSheetData() 函數
 */
export async function getBlogListSheetData(): Promise<string[][]> {
  const sheetId = GOOGLE_SHEETS_CONFIG.getBlogSheetId();
  if (!sheetId) {
    throw new Error('未設定部落格 Google Sheets ID，請檢查環境變數配置');
  }

  // 只讀取文章列表需要的欄位：A(title), D(author), E(thumbnail), F(time), G(seoSlug), I(seoDescription)
  const POSSIBLE_BLOG_LIST_RANGES = [
    'Blog!A:A,Blog!D:D,Blog!E:F,Blog!G:G,Blog!I:I',     // 英文名稱，分別讀取需要的欄位
    '部落格文章!A:A,部落格文章!D:D,部落格文章!E:F,部落格文章!G:G,部落格文章!I:I', // 中文名稱
    'Sheet1!A:A,Sheet1!D:D,Sheet1!E:F,Sheet1!G:G,Sheet1!I:I',   // 預設名稱
    'A:A,D:D,E:F,G:G,I:I'                               // 不指定工作表
  ];

  const client = getEdgeSheetsClient();

  try {
    console.log(`📡 從部落格 Google Sheets 讀取列表資料（優化版本）`);

    // 嘗試不同的範圍
    for (const range of POSSIBLE_BLOG_LIST_RANGES) {
      try {
        const data = await client.getValues(sheetId, range);
        if (data && data.length > 0) {
          return data;
        }
      } catch {
        // 繼續嘗試下一個範圍
        console.log(`嘗試範圍 ${range} 失敗，繼續下一個...`);
      }
    }

    throw new Error('所有範圍都無法讀取');

  } catch (error) {
    console.error('讀取部落格列表 Google Sheet 失敗:', error);
    throw new Error(`無法讀取部落格列表 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 從部落格專用的 Google Sheet 讀取文章詳情資料（Edge Runtime 版本）
 * 替代原有的 getBlogDetailSheetData() 函數
 */
export async function getBlogDetailSheetData(slug: string): Promise<string[][]> {
  const sheetId = GOOGLE_SHEETS_CONFIG.getBlogSheetId();
  if (!sheetId) {
    throw new Error('未設定部落格 Google Sheets ID，請檢查環境變數配置');
  }

  // 嘗試不同的工作表名稱
  const POSSIBLE_BLOG_RANGES = [
    'Blog!A:I',           // 英文名稱
    '部落格文章!A:I',     // 中文名稱
    'Sheet1!A:I',         // 預設名稱
    'A:I'                 // 不指定工作表
  ];

  const client = getEdgeSheetsClient();

  try {
    console.log(`📡 從部落格 Google Sheets 讀取文章詳情: ${slug}`);

    // 嘗試不同的範圍
    for (const range of POSSIBLE_BLOG_RANGES) {
      try {
        const data = await client.getValues(sheetId, range);
        if (data && data.length > 0) {
          return data;
        }
      } catch {
        // 繼續嘗試下一個範圍
        console.log(`嘗試範圍 ${range} 失敗，繼續下一個...`);
      }
    }

    throw new Error('所有範圍都無法讀取');

  } catch (error) {
    console.error('讀取部落格詳情 Google Sheet 失敗:', error);
    throw new Error(`無法讀取部落格詳情 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 從手錶專用的 Google Sheet 讀取手錶列表資料（優化版本，Edge Runtime）
 * 替代原有的 getWatchListSheetData() 函數
 */
export async function getWatchListSheetData(): Promise<string[][]> {
  const sheetId = GOOGLE_SHEETS_CONFIG.getWatchSheetId();
  if (!sheetId) {
    throw new Error('未設定手錶 Google Sheets ID，請檢查環境變數配置');
  }

  // 只讀取手錶列表需要的欄位：A(productName), B(brand), C(price), D(thumbnail), E(tag), F(availability), M(seoSlug)
  const POSSIBLE_WATCH_LIST_RANGES = [
    '手錶庫存!A:F,手錶庫存!M:M',     // 中文名稱，讀取 A~F 和 M 欄位
    'Watches!A:F,Watches!M:M',       // 英文名稱
    'Sheet1!A:F,Sheet1!M:M',         // 預設名稱
    'A:F,M:M'                        // 不指定工作表
  ];

  const client = getEdgeSheetsClient();

  try {
    console.log(`📡 從手錶 Google Sheets 讀取列表資料（優化版本）`);

    // 嘗試不同的範圍
    for (const range of POSSIBLE_WATCH_LIST_RANGES) {
      try {
        const data = await client.getValues(sheetId, range);
        if (data && data.length > 0) {
          return data;
        }
      } catch {
        // 繼續嘗試下一個範圍
        console.log(`嘗試範圍 ${range} 失敗，繼續下一個...`);
      }
    }

    throw new Error('所有範圍都無法讀取');

  } catch (error) {
    console.error('讀取手錶列表 Google Sheet 失敗:', error);
    throw new Error(`無法讀取手錶列表 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 從手錶專用的 Google Sheet 讀取手錶詳情資料（Edge Runtime 版本）
 * 替代原有的 getWatchDetailSheetData() 函數
 */
export async function getWatchDetailSheetData(slug: string): Promise<string[][]> {
  const sheetId = GOOGLE_SHEETS_CONFIG.getWatchSheetId();
  if (!sheetId) {
    throw new Error('未設定手錶 Google Sheets ID，請檢查環境變數配置');
  }

  // 嘗試不同的工作表名稱
  const POSSIBLE_WATCH_RANGES = [
    '手錶庫存!A:O',       // 中文名稱
    'Watches!A:O',        // 英文名稱
    'Sheet1!A:O',         // 預設名稱
    'A:O'                 // 不指定工作表
  ];

  const client = getEdgeSheetsClient();

  try {
    console.log(`📡 從手錶 Google Sheets 讀取手錶詳情: ${slug}`);

    // 嘗試不同的範圍
    for (const range of POSSIBLE_WATCH_RANGES) {
      try {
        const data = await client.getValues(sheetId, range);
        if (data && data.length > 0) {
          return data;
        }
      } catch {
        // 繼續嘗試下一個範圍
        console.log(`嘗試範圍 ${range} 失敗，繼續下一個...`);
      }
    }

    throw new Error('所有範圍都無法讀取');

  } catch (error) {
    console.error('讀取手錶詳情 Google Sheet 失敗:', error);
    throw new Error(`無法讀取手錶詳情 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}
