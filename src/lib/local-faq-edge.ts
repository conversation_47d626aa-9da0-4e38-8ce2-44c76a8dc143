import { FAQ, FAQListItem } from '@/types/faq';
import faqData from '@/data/faq.json';

// 記憶體快取
let faqCache: FAQ[] | null = null;
let tagsCache: string[] | null = null;

/**
 * 獲取所有 FAQ 資料 (Edge Runtime 版本)
 * 直接從打包後的 JSON 檔案讀取，不使用 Node.js fs API
 */
export function getAllFAQs(): FAQ[] {
  if (faqCache) {
    return faqCache;
  }

  try {
    // 直接使用 import 的 JSON 資料
    faqCache = faqData as FAQ[];
    return faqCache;
  } catch (error) {
    console.error('讀取 FAQ 資料失敗:', error);
    return [];
  }
}

/**
 * 搜尋 FAQ (Edge Runtime 版本)
 */
export function searchFAQs(query: string): FAQ[] {
  const allFAQs = getAllFAQs();
  
  if (!query.trim()) {
    return allFAQs;
  }

  const searchTerm = query.toLowerCase();
  
  return allFAQs.filter(faq => 
    faq.question.toLowerCase().includes(searchTerm) ||
    faq.answer.toLowerCase().includes(searchTerm) ||
    faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
}

/**
 * 獲取所有標籤 (Edge Runtime 版本)
 */
export function getAllTags(): string[] {
  if (tagsCache) {
    return tagsCache;
  }

  const allFAQs = getAllFAQs();
  const tagSet = new Set<string>();
  
  allFAQs.forEach(faq => {
    faq.tags.forEach(tag => tagSet.add(tag));
  });
  
  tagsCache = Array.from(tagSet).sort();
  return tagsCache;
}

/**
 * 根據標籤篩選 FAQ (Edge Runtime 版本)
 */
export function getFAQsByTag(tag: string): FAQ[] {
  const allFAQs = getAllFAQs();
  return allFAQs.filter(faq => faq.tags.includes(tag));
}

/**
 * 獲取 FAQ 列表項目 (Edge Runtime 版本)
 */
export function getFAQListItems(): FAQListItem[] {
  const allFAQs = getAllFAQs();
  return allFAQs.map(faq => ({
    id: faq.id,
    question: faq.question,
    tags: faq.tags,
    rawTag: faq.rawTag || faq.tags.join(', ')
  }));
}

/**
 * 根據 ID 獲取單一 FAQ (Edge Runtime 版本)
 */
export function getFAQById(id: string): FAQ | null {
  const allFAQs = getAllFAQs();
  return allFAQs.find(faq => faq.id === id) || null;
}

/**
 * 清除快取 (Edge Runtime 版本)
 */
export function clearFAQCache(): void {
  faqCache = null;
  tagsCache = null;
}
