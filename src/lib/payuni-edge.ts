/**
 * PayUni Edge Runtime 兼容版本
 * 使用 Web Crypto API 替代 Node.js crypto 模塊
 */

import { PAYUNI_CONFIG } from '@/config/environment-config';

// 將字符串轉換為 ArrayBuffer
function stringToArrayBuffer(str: string): Uint8Array {
  const encoder = new TextEncoder();
  return encoder.encode(str);
}

// 將 ArrayBuffer 轉換為字符串
function arrayBufferToString(buffer: ArrayBuffer): string {
  const decoder = new TextDecoder();
  return decoder.decode(buffer);
}

// 將 ArrayBuffer 轉換為十六進制字符串
function arrayBufferToHex(buffer: ArrayBuffer): string {
  const byteArray = new Uint8Array(buffer);
  const hexCodes = [...byteArray].map(value => {
    const hexCode = value.toString(16);
    const paddedHexCode = hexCode.padStart(2, '0');
    return paddedHexCode;
  });
  return hexCodes.join('');
}



// 將 base64 字符串轉換為 ArrayBuffer
function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
}

// 將 ArrayBuffer 轉換為 base64 字符串
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

// 將字符串轉換為十六進制
function stringToHex(str: string): string {
  const bytes = new TextEncoder().encode(str);
  return Array.from(bytes)
    .map(byte => byte.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * AES-GCM 加密 (Edge Runtime 兼容，與 Node.js 格式一致)
 */
async function encryptAESGCM(data: string, key: string, iv: string): Promise<string> {
  try {
    // 導入密鑰
    const keyBuffer = stringToArrayBuffer(key);
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      { name: 'AES-GCM' },
      false,
      ['encrypt']
    );

    // 加密
    const ivBuffer = stringToArrayBuffer(iv);
    const dataBuffer = stringToArrayBuffer(data);

    const encrypted = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: ivBuffer,
      },
      cryptoKey,
      dataBuffer
    );

    // 分離密文和 auth tag (GCM 模式的最後 16 字節是 auth tag)
    const encryptedArray = new Uint8Array(encrypted);
    const cipherText = encryptedArray.slice(0, -16);
    const authTag = encryptedArray.slice(-16);

    // 轉換為 base64 格式
    const cipherTextBase64 = arrayBufferToBase64(cipherText.buffer);
    const authTagBase64 = arrayBufferToBase64(authTag.buffer);

    // 使用與 Node.js 相同的格式：${cipherText}:::${tag}
    const combined = `${cipherTextBase64}:::${authTagBase64}`;

    // 轉換為 hex 格式
    return stringToHex(combined);
  } catch (error) {
    console.error('AES-GCM 加密失敗:', error);
    throw new Error('加密失敗');
  }
}

/**
 * AES-GCM 解密 (Edge Runtime 兼容)
 * 與 Node.js 版本保持一致的格式：hex -> Buffer -> string -> split(':::') -> base64 decode
 * 參考 Node.js 版本的邏輯：Buffer.from(encryptedHex, 'hex').toString().split(':::')
 */
async function decryptAESGCM(encryptedHex: string, key: string, iv: string): Promise<string> {
  try {
    console.log('🔓 開始解密，hex 長度:', encryptedHex.length);

    // 導入密鑰 (AES-256-GCM，所以密鑰是 32 字節)
    const keyBuffer = stringToArrayBuffer(key);
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      { name: 'AES-GCM' },
      false,
      ['decrypt']
    );

    // 模擬 Node.js 的 Buffer.from(encryptedHex, 'hex').toString()
    // 將 hex 轉換為字節，然後轉為字符串
    const hexBytes = new Uint8Array(encryptedHex.length / 2);
    for (let i = 0; i < encryptedHex.length; i += 2) {
      hexBytes[i / 2] = parseInt(encryptedHex.substring(i, i + 2), 16);
    }
    const hexString = new TextDecoder().decode(hexBytes);

    console.log('🔓 hex 轉字符串後的前 100 字符:', hexString.substring(0, 100));

    // 按 ':::' 分割
    const [encryptData, tag] = hexString.split(':::');

    if (!encryptData || !tag) {
      throw new Error('加密資料格式錯誤，無法找到 ::: 分隔符');
    }

    console.log('🔓 分割後 - encryptData 長度:', encryptData.length, 'tag 長度:', tag.length);

    // 將 base64 格式的加密資料和標籤轉換為 ArrayBuffer
    const encryptedBuffer = base64ToArrayBuffer(encryptData);
    const tagBuffer = base64ToArrayBuffer(tag);

    console.log('🔓 解碼後 - encrypted 長度:', encryptedBuffer.byteLength, 'tag 長度:', tagBuffer.byteLength);

    // 在 Web Crypto API 中，認證標籤需要附加到加密資料的末尾
    const combinedBuffer = new Uint8Array(encryptedBuffer.byteLength + tagBuffer.byteLength);
    combinedBuffer.set(new Uint8Array(encryptedBuffer), 0);
    combinedBuffer.set(new Uint8Array(tagBuffer), encryptedBuffer.byteLength);

    // 解密
    const ivBuffer = stringToArrayBuffer(iv);
    console.log('🔓 IV 長度:', ivBuffer.byteLength, '組合資料長度:', combinedBuffer.byteLength);

    const decrypted = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: ivBuffer,
      },
      cryptoKey,
      combinedBuffer
    );

    const result = arrayBufferToString(decrypted);
    console.log('🔓 解密成功，結果長度:', result.length);
    return result;
  } catch (error) {
    console.error('AES-GCM 解密失敗:', error);
    throw new Error('解密失敗');
  }
}

/**
 * SHA-256 雜湊 (Edge Runtime 兼容)
 * 根據 PayUni 官方文件格式: HashKey + EncryptInfo + HashIV
 */
async function sha256Hash(encryptInfo: string): Promise<string> {
  try {
    const HASH_KEY = PAYUNI_CONFIG.getHashKey();
    const HASH_IV = PAYUNI_CONFIG.getHashIV();

    if (!HASH_KEY || !HASH_IV) {
      throw new Error(`PayUni 憑證未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
    }

    // PayUni 官方格式: HashKey + EncryptInfo + HashIV
    const hashData = `${HASH_KEY}${encryptInfo}${HASH_IV}`;
    const dataBuffer = stringToArrayBuffer(hashData);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    return arrayBufferToHex(hashBuffer).toUpperCase(); // PayUni 要求大寫
  } catch (error) {
    console.error('SHA-256 雜湊失敗:', error);
    throw new Error('雜湊失敗');
  }
}

/**
 * 建立 PayUni 查詢請求 (Edge Runtime 兼容)
 */
export async function createQueryRequestEdge(orderNo: string): Promise<{
  EncryptInfo: string;
  HashInfo: string;
  ApiUrl: string;
}> {
  const queryData = {
    MerID: PAYUNI_CONFIG.getMerchantId(),
    MerTradeNo: orderNo,
    Timestamp: Math.floor(Date.now() / 1000) - 60, // 提前 60 秒避免時間差
  };

  console.log('🔍 建立 PayUni 查詢請求:', queryData);

  // 將查詢資料轉換為查詢字符串格式
  const queryString = Object.entries(queryData)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');

  console.log('📝 查詢字符串:', queryString);

  // 加密查詢資料
  const encryptInfo = await encryptAESGCM(
    queryString,
    PAYUNI_CONFIG.getHashKey(),
    PAYUNI_CONFIG.getHashIV()
  );

  // 建立 Hash 資料 (PayUni 官方格式)
  const hashInfo = await sha256Hash(encryptInfo);

  console.log('🔐 加密完成:', {
    EncryptInfo: encryptInfo.substring(0, 50) + '...',
    HashInfo: hashInfo.substring(0, 50) + '...'
  });

  return {
    EncryptInfo: encryptInfo,
    HashInfo: hashInfo,
    ApiUrl: PAYUNI_CONFIG.getQueryUrl()
  };
}

/**
 * 解密 PayUni 回應 (Edge Runtime 兼容)
 */
export async function decryptPayUniDataEdge(encryptInfo: string, hashInfo: string): Promise<Record<string, string>> {
  try {
    console.log('🔓 開始解密 PayUni 回應...');

    // 驗證 Hash (PayUni 官方格式)
    const calculatedHash = await sha256Hash(encryptInfo);

    if (calculatedHash.toLowerCase() !== hashInfo.toLowerCase()) {
      throw new Error('Hash 驗證失敗');
    }

    // 解密資料
    const decryptedString = await decryptAESGCM(
      encryptInfo,
      PAYUNI_CONFIG.getHashKey(),
      PAYUNI_CONFIG.getHashIV()
    );

    console.log('✅ 解密成功:', decryptedString);

    // 解析查詢字符串為物件
    const params = new URLSearchParams(decryptedString);
    const result: Record<string, string> = {};
    
    for (const [key, value] of params.entries()) {
      result[key] = value;
    }

    return result;
  } catch (error) {
    console.error('❌ PayUni 解密失敗:', error);
    throw error;
  }
}

/**
 * 建立 PayUni 付款請求 (Edge Runtime 兼容)
 */
export async function createPaymentRequestEdge(params: {
  orderNo: string;
  eventName: string;
  eventPrice: number;
  userName: string;
  userEmail: string;
  expireDate: string;
}): Promise<{
  EncryptInfo: string;
  HashInfo: string;
  ApiUrl: string;
  MerID: string;
}> {
  const { orderNo, eventName, eventPrice, userEmail, expireDate } = params;

  // 建立 PayUni 付款請求所需的資料
  const tradeData = {
    MerID: PAYUNI_CONFIG.getMerchantId(),
    MerTradeNo: orderNo,
    TradeAmt: eventPrice,
    Timestamp: Math.floor(Date.now() / 1000) - 30, // 提前 30 秒避免時間差
    ProdDesc: eventName,
    UsrMail: userEmail,
    Lang: 'zh-tw',
    NotifyURL: process.env.PAYUNI_NOTIFY_URL || '',
    ReturnURL: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback`,
    BackURL: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback`,
    PaymentType: 'credit,atm',
    CreditType: '1',
    ExpireDate: expireDate,
  };

  console.log('🔄 建立 PayUni 付款請求:', tradeData);

  // 將交易資料轉換為查詢字符串格式
  const tradeString = Object.entries(tradeData)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');

  // 加密交易資料
  const encryptInfo = await encryptAESGCM(
    tradeString,
    PAYUNI_CONFIG.getHashKey(),
    PAYUNI_CONFIG.getHashIV()
  );

  // 建立 Hash 資料 (PayUni 官方格式)
  const hashInfo = await sha256Hash(encryptInfo);

  console.log('🔐 付款請求加密完成');

  return {
    EncryptInfo: encryptInfo,
    HashInfo: hashInfo,
    ApiUrl: PAYUNI_CONFIG.getApiUrl(), // 使用付款 API 端點，不是查詢端點
    MerID: PAYUNI_CONFIG.getMerchantId()
  };
}

/**
 * 計算 ATM 到期日 (Edge Runtime 兼容)
 */
export function calculateATMExpireDateEdge(): string {
  const now = new Date();
  const taiwanTime = new Date(now.getTime() + (8 * 60 * 60 * 1000)); // UTC+8
  const hour = taiwanTime.getHours();

  console.log(`🕐 當前台灣時間: ${taiwanTime.toISOString()}, 小時: ${hour}`);

  let daysToAdd: number;
  if (hour >= 14) {
    // 下午 2 點後，+3 天
    daysToAdd = 3;
    console.log('⏰ 台灣時間 14:xx 後，設定 ATM 到期日為當日+3天');
  } else {
    // 下午 2 點前，+2 天
    daysToAdd = 2;
    console.log('⏰ 台灣時間 14:xx 前，設定 ATM 到期日為當日+2天');
  }

  const expireDate = new Date(taiwanTime);
  expireDate.setDate(expireDate.getDate() + daysToAdd);

  const formattedDate = expireDate.toISOString().split('T')[0];
  console.log(`📅 計算出的 ATM 到期日期: ${formattedDate}`);

  return formattedDate;
}

/**
 * 查詢 PayUni 訂單 (Edge Runtime 兼容)
 */
export async function queryPayUniOrderEdge(orderNo: string): Promise<unknown> {
  try {
    console.log(`🔍 開始查詢 PayUni 訂單: ${orderNo}`);

    // 建立查詢請求
    const queryRequest = await createQueryRequestEdge(orderNo);

    console.log('📡 發送查詢請求到:', queryRequest.ApiUrl);

    // 發送查詢請求
    const response = await fetch(queryRequest.ApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'payuni',
      },
      body: new URLSearchParams({
        MerID: PAYUNI_CONFIG.getMerchantId(),
        Version: '2.0',
        EncryptInfo: queryRequest.EncryptInfo,
        HashInfo: queryRequest.HashInfo,
      }),
    });

    console.log('📊 PayUni API 回應狀態:', response.status);

    if (!response.ok) {
      throw new Error(`PayUni API 錯誤: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('📄 PayUni API 回應內容 (前 200 字元):', responseText.substring(0, 200));

    // 檢查是否為 HTML 回應
    if (responseText.trim().startsWith('<!DOCTYPE') || responseText.trim().startsWith('<html')) {
      console.log('⚠️ PayUni API 返回 HTML，可能是 API 端點錯誤');
      throw new Error('PayUni API 返回無效格式');
    }

    // 解析 JSON 回應
    const responseData = JSON.parse(responseText);
    console.log('📦 解析後的回應:', responseData);

    // 如果查詢成功，解密回應資料
    if (responseData.Status === 'SUCCESS' && responseData.EncryptInfo && responseData.HashInfo) {
      const decryptedData = await decryptPayUniDataEdge(responseData.EncryptInfo, responseData.HashInfo);
      console.log('🔓 解密後的訂單資料:', decryptedData);
      return decryptedData;
    } else {
      console.log('❌ PayUni 查詢失敗:', responseData);
      throw new Error(responseData.Message || 'PayUni 查詢失敗');
    }

  } catch (error) {
    console.error('❌ 查詢 PayUni 訂單失敗:', error);
    throw error;
  }
}
