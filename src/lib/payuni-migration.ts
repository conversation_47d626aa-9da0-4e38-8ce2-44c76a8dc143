/**
 * PayUni API 遷移相容層
 * 提供與原有 payuni.ts 相同的介面，但使用 Edge Runtime 相容實作
 */

import {
  createPaymentRequestEdge,
  queryPayUniOrderEdge,
  calculateATMExpireDateEdge
} from './payuni-edge';

/**
 * 建立 PayUni 付款請求（相容原有介面）
 */
export async function createPaymentRequest(
  orderData: {
    MerchantOrderNo: string;
    Amt: number;
    ItemDesc: string;
    Email: string;
    LoginType?: number;
    CREDIT?: number;
    VACC?: number;
    CVS?: number;
    BARCODE?: number;
    ExpireDate?: string;
    NotifyURL?: string;
    ReturnURL?: string;
    CustomerURL?: string;
  }
): Promise<{
  MerchantID: string;
  TradeInfo: string;
  TradeSha: string;
  Version: string;
  paymentUrl: string;
}> {
  // 調用 Edge 版本的函數
  const edgeResult = await createPaymentRequestEdge({
    orderNo: orderData.MerchantOrderNo,
    eventName: orderData.ItemDesc,
    eventPrice: orderData.Amt,
    userName: '', // 原有介面沒有提供用戶名稱
    userEmail: orderData.Email,
    expireDate: orderData.ExpireDate || calculateATMExpireDate()
  });

  // 轉換回原有介面格式
  return {
    MerchantID: edgeResult.MerID,
    TradeInfo: edgeResult.EncryptInfo,
    TradeSha: edgeResult.HashInfo,
    Version: '2.0',
    paymentUrl: edgeResult.ApiUrl
  };
}

/**
 * 查詢 PayUni 訂單狀態（相容原有介面）
 */
export async function queryPayUniOrder(orderNo: string): Promise<unknown> {
  return await queryPayUniOrderEdge(orderNo);
}

/**
 * 解析 PayUni Webhook 回應（相容原有介面）
 */
export function parseWebhookResponse(encryptedData: { EncryptInfo: string; HashInfo: string }): Record<string, unknown> | null {
  // 使用 Edge Runtime 相容的解密函數
  try {
    const { EncryptInfo, HashInfo } = encryptedData;

    // 這裡應該調用 Edge Runtime 相容的解密函數
    // 暫時返回模擬數據，實際應該實作解密邏輯
    console.log('🔓 解析 PayUni Webhook 數據:', { EncryptInfo: EncryptInfo.substring(0, 50) + '...', HashInfo });

    // TODO: 實作 Edge Runtime 相容的 PayUni 解密邏輯
    // 目前返回 null 表示需要實作
    return null;
  } catch (error) {
    console.error('❌ 解析 PayUni Webhook 失敗:', error);
    return null;
  }
}

/**
 * 計算 ATM 轉帳到期日（相容原有介面）
 */
export function calculateATMExpireDate(): string {
  return calculateATMExpireDateEdge();
}

/**
 * 轉換交易狀態（相容原有介面）
 */
export function convertTradeStatus(status: string): string {
  const statusMap: { [key: string]: string } = {
    '0': '未付款',
    '1': '付款成功',
    '2': '付款失敗',
    '3': '已取消',
    '6': '退款',
    '10': 'ATM 待付款',
    '11': 'ATM 已過期'
  };

  return statusMap[status] || `未知狀態 (${status})`;
}

/**
 * 轉換付款方式（相容原有介面）
 */
export function convertPaymentType(paymentType: string): string {
  const typeMap: { [key: string]: string } = {
    'CREDIT': '信用卡',
    'VACC': 'ATM 轉帳',
    'CVS': '超商代碼',
    'BARCODE': '超商條碼'
  };

  return typeMap[paymentType] || paymentType;
}

/**
 * 取得整體付款狀態（相容原有介面）
 */
export function getOverallPaymentStatus(
  tradeStatus: string,
  closeStatus?: string,
  refundStatus?: string
): {
  status: string;
  displayText: string;
  showRefund: boolean;
} {
  // 檢查是否有退款資訊
  const showRefund = closeStatus === '2' || refundStatus === '2';

  if (showRefund) {
    return {
      status: 'refunded',
      displayText: '已退款',
      showRefund: true
    };
  }

  // 根據交易狀態判斷
  switch (tradeStatus) {
    case '1':
      return {
        status: 'paid',
        displayText: '付款成功',
        showRefund: false
      };
    case '0':
    case '10':
      return {
        status: 'pending',
        displayText: '待付款',
        showRefund: false
      };
    case '2':
      return {
        status: 'failed',
        displayText: '付款失敗',
        showRefund: false
      };
    case '3':
      return {
        status: 'cancelled',
        displayText: '已取消',
        showRefund: false
      };
    case '11':
      return {
        status: 'expired',
        displayText: 'ATM 已過期',
        showRefund: false
      };
    default:
      return {
        status: 'unknown',
        displayText: `未知狀態 (${tradeStatus})`,
        showRefund: false
      };
  }
}
