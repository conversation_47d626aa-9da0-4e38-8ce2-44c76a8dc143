import crypto from 'crypto';
import querystring from 'querystring';
import { PAYUNI_CONFIG } from '@/config/environment-config';

/**
 * AES-256-GCM 加密 (PayUni 使用的模式)
 * @param {string} plaintext - 要加密的參數
 * @returns {string} - 加密結果 (hex)
 */
function encryptAES(plaintext: string): string {
  const HASH_KEY = PAYUNI_CONFIG.getHashKey();
  const HASH_IV = PAYUNI_CONFIG.getHashIV();

  if (!HASH_KEY || !HASH_IV) {
    throw new Error(`PayUni 憑證未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
  }

  const iv = Buffer.from(HASH_IV);
  const cipher = crypto.createCipheriv('aes-256-gcm', HASH_KEY, iv);

  let cipherText = cipher.update(plaintext, 'utf8', 'base64');
  cipherText += cipher.final('base64');

  const tag = cipher.getAuthTag().toString('base64');
  return Buffer.from(`${cipherText}:::${tag}`).toString('hex').trim();
}

/**
 * AES-256-GCM 解密 (PayUni 使用的模式)
 * @param {string} encryptedHex - 要解密的參數 (hex)
 * @returns {string} - 解密結果
 */
function decryptAES(encryptedHex: string): string {
  const HASH_KEY = PAYUNI_CONFIG.getHashKey();
  const HASH_IV = PAYUNI_CONFIG.getHashIV();

  if (!HASH_KEY || !HASH_IV) {
    throw new Error(`PayUni 憑證未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
  }

  const iv = Buffer.from(HASH_IV);
  const [encryptData, tag] = Buffer.from(encryptedHex, 'hex').toString().split(':::');

  const decipher = crypto.createDecipheriv('aes-256-gcm', HASH_KEY, iv);
  decipher.setAuthTag(Buffer.from(tag, 'base64'));

  let decipherText = decipher.update(encryptData, 'base64', 'utf8');
  decipherText += decipher.final('utf8');

  return decipherText;
}

/**
 * SHA256 加密 (根據 PayUni 官方文件格式)
 * @param {string} encryptStr - 加密過後的參數
 * @returns {string} - hash 結果的字串，16進制且皆為大寫
 */
function sha256(encryptStr: string): string {
  const HASH_KEY = PAYUNI_CONFIG.getHashKey();
  const HASH_IV = PAYUNI_CONFIG.getHashIV();

  if (!HASH_KEY || !HASH_IV) {
    throw new Error(`PayUni 憑證未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
  }

  const hash = crypto.createHash('sha256').update(`${HASH_KEY}${encryptStr}${HASH_IV}`);
  return hash.digest('hex').toUpperCase();
}

/**
 * 計算 PayUni ATM 轉帳的動態到期日期
 *
 * 業務邏輯：
 * - 台灣時間下午 2:00 PM (14:00) 之後發起的交易：當日+3天
 * - 台灣時間下午 2:00 PM (14:00) 之前或等於發起的交易：當日+2天
 *
 * @returns {string} 格式化的到期日期 (YYYY-MM-DD)
 */
export function calculateATMExpireDate(): string {
  try {
    // 取得當前台灣時間 (UTC+8)
    const now = new Date();
    const taiwanTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));

    // 取得台灣時間的小時數
    const taiwanHour = taiwanTime.getUTCHours();

    console.log(`🕐 當前台灣時間: ${taiwanTime.toISOString().replace('Z', '+08:00')}, 小時: ${taiwanHour}`);

    // 判斷是否為下午 2:00 (14:00) 之後
    const isAfter2PM = taiwanHour >= 14;

    // 計算到期日期
    const today = new Date();
    const expireDate = new Date(today);

    if (isAfter2PM) {
      // 下午 2:00 之後：當日+3天
      expireDate.setDate(today.getDate() + 3);
      console.log(`⏰ 台灣時間 ${taiwanHour}:xx (≥14:00)，設定 ATM 到期日為當日+3天`);
    } else {
      // 下午 2:00 之前或等於：當日+2天
      expireDate.setDate(today.getDate() + 2);
      console.log(`⏰ 台灣時間 ${taiwanHour}:xx (<14:00)，設定 ATM 到期日為當日+2天`);
    }

    const formattedDate = expireDate.toISOString().split('T')[0];
    console.log(`📅 計算出的 ATM 到期日期: ${formattedDate}`);

    return formattedDate;

  } catch (error) {
    // 錯誤處理：回退到安全預設值（當日+2天）
    console.error('❌ 計算 ATM 到期日期時發生錯誤，使用預設值（當日+2天）:', error);

    const today = new Date();
    const fallbackDate = new Date(today);
    fallbackDate.setDate(today.getDate() + 2);
    const formattedFallbackDate = fallbackDate.toISOString().split('T')[0];

    console.log(`🔄 使用回退預設值: ${formattedFallbackDate}`);
    return formattedFallbackDate;
  }
}

/**
 * 建立 PayUni 付款請求所需參數
 * @param tradeData - 包含訂單資訊的物件
 * @returns {{ MerID: string, EncryptInfo: string, HashInfo: string }}
 */
export function createPaymentRequest(tradeData: Record<string, string | number | boolean>) {
  const merID = PAYUNI_CONFIG.getMerchantId();

  if (!merID) {
    throw new Error(`PayUni 商店 ID 未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
  }

  const encryptInfoString = querystring.stringify(tradeData);
  const encryptInfo = encryptAES(encryptInfoString);
  const hashInfo = sha256(encryptInfo);

  return {
    MerID: merID,
    EncryptInfo: encryptInfo,
    HashInfo: hashInfo,
  };
}

/**
 * 解析 PayUni Webhook 通知
 * @param encryptedData - 包含 EncryptInfo 和 HashInfo 的物件
 * @returns {Record<string, unknown> | null} - 解密後的訂單資訊，或在驗證失敗時返回 null
 */
export function parseWebhookResponse(encryptedData: { EncryptInfo: string; HashInfo: string }): Record<string, unknown> | null {
  const { EncryptInfo, HashInfo } = encryptedData;

  const calculatedHash = sha256(EncryptInfo);

  if (calculatedHash !== HashInfo) {
    console.error('PayUni Webhook SHA256 驗證失敗');
    return null;
  }

  const decryptedString = decryptAES(EncryptInfo);
  return querystring.parse(decryptedString);
}

/**
 * 轉換 PayUni 訂單狀態碼為中文
 * @param tradeStatus - PayUni TradeStatus 代碼
 * @returns {string} - 中文狀態描述
 */
export function convertTradeStatus(tradeStatus: string | number): string {
  const status = String(tradeStatus);
  switch (status) {
    case '0': return '取號成功';
    case '1': return '已付款';
    case '2': return '付款失敗';
    case '3': return '付款取消';
    case '4': return '交易逾期';
    case '8': return '訂單待確認';
    case '9': return '未付款';
    default: return '未知狀態';
  }
}

/**
 * 轉換 PayUni 支付方式代碼為中文
 * @param paymentType - PayUni PaymentType 代碼
 * @returns {string} - 中文支付方式描述
 */
export function convertPaymentType(paymentType: string | number): string {
  const type = String(paymentType);
  switch (type) {
    case '1': return '信用卡';
    case '2': return 'ATM轉帳';
    case '3': return '條碼/代碼';
    case '5': return '取貨付款';
    case '6': return '愛金卡';
    case '7': return '後支付';
    case '8': return '退貨代收';
    case '9': return 'LINEPay';
    case '10': return '宅配到付';
    case '11': return '街口支付';
    default: return '未知支付方式';
  }
}

/**
 * 轉換 PayUni 退款狀態代碼為中文
 * @param refundStatus - PayUni RefundStatus 代碼
 * @returns {string} - 中文退款狀態描述
 */
export function convertRefundStatus(refundStatus: string | number): string {
  const status = String(refundStatus);
  switch (status) {
    case '1': return '退款申請中';
    case '2': return '已退款';
    case '3': return '退款取消';
    case '8': return '退款處理中';
    default: return '未知退款狀態';
  }
}

/**
 * 綜合判斷 PayUni 訂單的整體付款狀態
 * 考慮交易狀態、退款狀態等因素
 * @param payuniData - PayUni API 回應資料
 * @returns {string} - 綜合付款狀態
 */
export function getOverallPaymentStatus(payuniData: Record<string, unknown>): string {
  const tradeStatus = String(payuniData.TradeStatus || '');
  const refundStatus = String(payuniData.RefundStatus || '');
  const refundAmt = parseInt(String(payuniData.RefundAmt || '0'));

  // 優先檢查退款狀態 - 統一顯示為"已退款"
  if (refundStatus === '2' && refundAmt > 0) {
    return '已退款';
  } else if (refundStatus === '1' || refundStatus === '8') {
    // 退款處理中
    return '退款處理中';
  }

  // 如果沒有退款，則根據交易狀態判斷
  switch (tradeStatus) {
    case '0': return '取號成功';
    case '1': return '已付款';
    case '2': return '付款失敗';
    case '3': return '付款取消';
    case '4': return '交易逾期';
    case '8': return '訂單待確認';
    case '9': return '待付款';
    default: return '未知狀態';
  }
}

/**
 * PayUni 查詢參數介面
 */
interface PayUniQueryParams {
  orderNo?: string;        // 商店訂單編號 (MerTradeNo)
  tradeNo?: string;        // PayUni 交易編號 (TradeNo)
  email?: string;          // 用戶 Email (UsrMail)
  startDate?: string;      // 查詢開始日期 (可選)
  endDate?: string;        // 查詢結束日期 (可選)
}

/**
 * 建立 PayUni 查詢請求所需參數
 * @param params - 查詢參數物件
 * @returns {{ MerID: string, Version: string, EncryptInfo: string, HashInfo: string }}
 */
export function createQueryRequest(params: PayUniQueryParams) {
  const merID = PAYUNI_CONFIG.getMerchantId();

  if (!merID) {
    throw new Error(`PayUni 商店 ID 未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
  }

  // 建立查詢資料物件
  const queryData: Record<string, string | number> = {
    MerID: merID,
    Timestamp: Math.floor(Date.now() / 1000) - 60, // 時間戳記，提前 60 秒避免過期
  };

  if (params.orderNo) {
    queryData.MerTradeNo = params.orderNo; // 商店訂單編號
  }

  if (params.tradeNo) {
    queryData.TradeNo = params.tradeNo;
  }

  if (params.email) {
    queryData.UsrMail = params.email;
  }

  if (params.startDate) {
    queryData.StartDate = params.startDate;
  }

  if (params.endDate) {
    queryData.EndDate = params.endDate;
  }

  // 確保至少有一個查詢條件
  if (!params.orderNo && !params.tradeNo && !params.email) {
    throw new Error('至少需要提供一個查詢條件：orderNo、tradeNo 或 email');
  }

  const encryptInfoString = querystring.stringify(queryData);
  const encryptInfo = encryptAES(encryptInfoString);
  const hashInfo = sha256(encryptInfo);

  return {
    MerID: merID,
    Version: '2.0', // 查詢 API 使用 Version 2.0
    EncryptInfo: encryptInfo,
    HashInfo: hashInfo,
  };
}

/**
 * 建立 PayUni 查詢請求所需參數 (舊版本，保持向後相容)
 * @param orderNo - 商店訂單編號 (MerTradeNo)
 * @returns {{ MerID: string, Version: string, EncryptInfo: string, HashInfo: string }}
 */
export function createQueryRequestByOrderNo(orderNo: string) {
  return createQueryRequest({ orderNo });
}

/**
 * 查詢 PayUni 訂單狀態 (增強版，支援多種查詢參數)
 * @param params - 查詢參數物件
 * @returns {Promise<Record<string, unknown> | null>} - 查詢結果或 null
 */
export async function queryPayUniOrder(params: PayUniQueryParams): Promise<Record<string, unknown> | null> {
  try {
    const queryRequest = createQueryRequest(params);
    const queryUrl = PAYUNI_CONFIG.getQueryUrl();

    const queryDescription = [];
    if (params.orderNo) queryDescription.push(`訂單號: ${params.orderNo}`);
    if (params.tradeNo) queryDescription.push(`交易號: ${params.tradeNo}`);
    if (params.email) queryDescription.push(`Email: ${params.email}`);

    console.log(`🔍 查詢 PayUni 訂單: ${queryDescription.join(', ')}，API: ${queryUrl}`);

    const response = await fetch(queryUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'payuni',
      },
      body: new URLSearchParams({
        MerID: queryRequest.MerID,
        Version: queryRequest.Version,
        EncryptInfo: queryRequest.EncryptInfo,
        HashInfo: queryRequest.HashInfo,
      }),
    });

    if (!response.ok) {
      console.error(`❌ PayUni API 回應錯誤: ${response.status} ${response.statusText}`);
      return null;
    }

    const responseData = await response.json();
    console.log('📥 PayUni API 原始回應:', responseData);

    // 驗證回應格式
    if (!responseData.EncryptInfo || !responseData.HashInfo) {
      console.error('❌ PayUni API 回應格式錯誤，缺少 EncryptInfo 或 HashInfo');
      return null;
    }

    // 驗證 Hash
    const calculatedHash = sha256(responseData.EncryptInfo);
    if (calculatedHash !== responseData.HashInfo) {
      console.error('❌ PayUni API 回應 SHA256 驗證失敗');
      return null;
    }

    // 解密資料
    const decryptedString = decryptAES(responseData.EncryptInfo);
    const decryptedData = querystring.parse(decryptedString);

    console.log('🔓 PayUni 查詢結果:', decryptedData);
    return decryptedData;

  } catch (error) {
    console.error('❌ 查詢 PayUni 訂單失敗:', error);
    return null;
  }
}

/**
 * 查詢 PayUni 訂單狀態 (舊版本，保持向後相容)
 * @param orderNo - 商店訂單編號
 * @returns {Promise<Record<string, unknown> | null>} - 查詢結果或 null
 */
export async function queryPayUniOrderByOrderNo(orderNo: string): Promise<Record<string, unknown> | null> {
  return queryPayUniOrder({ orderNo });
}



/**
 * 解密 PayUni 返回的資料 (用於前景返回頁面)
 * @param encryptInfo - PayUni 返回的 EncryptInfo 參數
 * @param hashKey - PayUni Hash Key
 * @param hashIV - PayUni Hash IV
 * @returns {Record<string, unknown>} - 解密後的訂單資訊
 */
export function decryptPayUniData(encryptInfo: string, hashKey: string, hashIV: string): Record<string, unknown> {
  try {
    console.log('解密參數:', {
      encryptInfoLength: encryptInfo.length,
      hashKeyLength: hashKey.length,
      hashIVLength: hashIV.length
    });

    const iv = Buffer.from(hashIV);
    const decryptedHex = Buffer.from(encryptInfo, 'hex').toString();

    console.log('解密中間步驟:', {
      decryptedHex,
      containsDelimiter: decryptedHex.includes(':::')
    });

    const parts = decryptedHex.split(':::');
    if (parts.length !== 2) {
      throw new Error(`加密資料格式錯誤，預期格式為 "data:::tag"，實際收到: ${parts.length} 個部分`);
    }

    const [encryptData, tag] = parts;

    if (!encryptData || !tag) {
      throw new Error('加密資料或標籤為空');
    }

    const decipher = crypto.createDecipheriv('aes-256-gcm', hashKey, iv);
    decipher.setAuthTag(Buffer.from(tag, 'base64'));

    let decipherText = decipher.update(encryptData, 'base64', 'utf8');
    decipherText += decipher.final('utf8');

    console.log('解密成功，原始資料:', decipherText);

    return querystring.parse(decipherText);
  } catch (error) {
    console.error('PayUni 解密詳細錯誤:', {
      error: error instanceof Error ? error.message : error,
      encryptInfo: encryptInfo.substring(0, 100) + '...',
      stack: error instanceof Error ? error.stack : undefined
    });
    throw new Error(`PayUni 解密失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
}


