// MDX 部落格文章類型定義

export interface BlogFrontmatter {
  title: string;
  publishDate: string;
  author: string;
  thumbnail: string;
  seoTitle?: string;
  seoDescription?: string;
  socialImage?: string;
  socialTitle?: string;
  socialDescription?: string;
  slug: string;
  tags?: string[];
  excerpt?: string;
  featured?: boolean;
}

export interface MDXBlogPost {
  frontmatter: BlogFrontmatter;
  content: string;
  slug: string;
  readingTime?: number;
}

export interface MDXBlogListItem {
  frontmatter: BlogFrontmatter;
  slug: string;
  excerpt: string;
  readingTime?: number;
}

// MDX 組件 props 類型
export interface MDXComponentProps {
  children?: React.ReactNode;
  className?: string;
}

// 用於 MDX 內容的自定義組件類型
export interface MDXComponents {
  h1?: React.ComponentType<MDXComponentProps>;
  h2?: React.ComponentType<MDXComponentProps>;
  h3?: React.ComponentType<MDXComponentProps>;
  p?: React.ComponentType<MDXComponentProps>;
  img?: React.ComponentType<MDXComponentProps & { src?: string; alt?: string }>;
  a?: React.ComponentType<MDXComponentProps & { href?: string }>;
  blockquote?: React.ComponentType<MDXComponentProps>;
  code?: React.ComponentType<MDXComponentProps>;
  pre?: React.ComponentType<MDXComponentProps>;
  ul?: React.ComponentType<MDXComponentProps>;
  ol?: React.ComponentType<MDXComponentProps>;
  li?: React.ComponentType<MDXComponentProps>;
}

// 部落格 API 回應類型
export interface MDXBlogResponse {
  posts: MDXBlogListItem[];
  total: number;
  hasMore: boolean;
  page: number;
  pageSize: number;
}

// 檔案系統相關類型
export interface BlogFileInfo {
  filename: string;
  slug: string;
  fullPath: string;
  lastModified: Date;
}
